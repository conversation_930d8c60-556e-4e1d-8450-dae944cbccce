<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class WelcomeAboardNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct()
    {
        //
    }

    public function via($notifiable): array
    {
        return ['database', FcmChannel::class];
    }

    public function toFcm($notifiable): FcmMessage
    {
        info("Sending welcome notification to {$notifiable->name}");

        return new FcmMessage(notification: new FcmNotification(
            title: 'Welcome Aboard! 🎉',
            body: 'Welcome to Conari! Discover amazing deals and experiences near you. Start exploring now!',
        ))
            ->data([
                'type' => 'welcome_aboard',
                'user_id' => (string) $notifiable->id,
                'deep_link' => 'conari://home',
            ])
            ->custom([
                'android' => [
                    'notification' => [
                        'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                        'color' => '#7569F3',
                        'sound' => 'notification',
                        'icon' => 'notification_icon',
                        'channel_id' => 'welcome',
                    ],
                    'fcm_options' => [
                        'analytics_label' => 'welcome_aboard',
                    ],
                ],
                'apns' => [
                    'payload' => [
                        'aps' => [
                            'category' => 'WELCOME_ABOARD',
                            'sound' => 'default',
                        ],
                    ],
                    'fcm_options' => [
                        'analytics_label' => 'welcome_aboard_ios',
                    ],
                ],
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'welcome_aboard',
            'title' => 'Welcome Aboard! 🎉',
            'message' => 'Welcome to Conari! Discover amazing deals and experiences near you.',
            'user_id' => $notifiable->id,
        ];
    }
}
