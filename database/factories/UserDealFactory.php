<?php

namespace Database\Factories;

use App\Models\Deal;
use App\Models\User;
use App\Models\UserDeal;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserDealFactory extends Factory
{
    protected $model = UserDeal::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'deal_id' => Deal::factory(),
            'status' => 'upcoming',
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    public function upcoming(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'upcoming',
            ];
        });
    }

    public function redeemable(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'redeemable',
            ];
        });
    }

    public function redeemed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'redeemed',
        ]);
    }

    public function noShow(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'no-show',
        ]);
    }
}
