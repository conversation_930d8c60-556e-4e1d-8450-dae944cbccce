<?php

namespace App\Models;

use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes; // Add this line
use Illuminate\Foundation\Auth\User as Authenticatable;

class Employee extends Authenticatable implements FilamentUser
{
    use HasFactory, SoftDeletes; // Add SoftDeletes here

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'password' => 'hashed',
            'deleted_at' => 'datetime', // Add this line
        ];
    }

    public function admin(): BelongsTo
    {
        return $this->belongsTo(Admin::class);
    }

    public function places(): BelongsToMany
    {
        return $this->belongsToMany(PartnerLocation::class, 'employee_place', 'employee_id', 'place_id');
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return $panel->getId() === 'employee';
    }
}
