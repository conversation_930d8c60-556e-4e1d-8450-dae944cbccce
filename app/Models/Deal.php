<?php

namespace App\Models;

use App\Enums\DealType;
use App\Traits\HasApprovalWorkflow;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/** @property Collection $valid_days */
/** @property ?UserDeal $deal */
/** @property Tag $service_type */
/** @property Collection $service_types */
/** @property Collection $service_options */
/** @property Deal $deal */
/** @property array $available_slots */
class Deal extends Model
{
    use HasFactory, SoftDeletes, HasApprovalWorkflow;

    protected $touches = ['partner_place'];

    protected $guarded = ['id'];

    protected static function booted(): void
    {
        static::deleting(function (Deal $deal) {
            // Delete all user-deal relationships
            UserDeal::where('deal_id', $deal->id)->whereNotIn('status', ['upcoming', 'redeemable'])->delete();
        });
    }

    protected function casts(): array
    {
        return [
            'deal_type' => DealType::class,
            'valid_days' => 'collection',
            'deleted_at' => 'datetime',
        ];
    }

    public function partner_place(): BelongsTo
    {
        return $this->belongsTo(PartnerLocation::class, 'partner_location_id');
    }

    public function dealSlots(): HasMany
    {
        return $this->hasMany(DealSlot::class, 'deal_id');
    }

    public function service_type(): BelongsTo
    {
        return $this->belongsTo(Tag::class, 'service_type_id');
    }

    #[\Deprecated('Use service_options() instead')]
    public function service_types(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'deal_tag')->where('type', \App\Enums\TagType::SERVICE_OPTIONS->value);
    }

    public function service_options(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'deal_tag')->where('type', \App\Enums\TagType::SERVICE_OPTIONS->value);
    }

    public function toSearchableArray(): array
    {
        /** @var Collection<?Tag> $serviceTypes */
        $serviceTypes = $this->service_options;

        /** @var DealType $dealType */
        $dealType = $this->deal_type;

        $data = [
            'id' => (string) $this->id,
            'deal_type' => $dealType->name,
            'title' => $this->title,
            'slots' => $this->dealSlots->groupBy('day')
                ->mapWithKeys(function (Collection $slots, $day) {
                    return [
                        /** @phpstan-ignore-next-line */
                        strtolower(Carbon::getDays()[$day]) => $slots->map(function (DealSlot $slot) {
                            return [
                                'from' => $slot->from,
                                'to' => $slot->to,
                            ];
                        })->toArray(),
                    ];
                })->toArray(),
        ];

        if ($serviceTypes->isNotEmpty()) {
            $data['service_types'] = $serviceTypes->pluck('title')->toArray();
        }

        return $data;
    }

    protected function availableSlots(): Attribute
    {
        return Attribute::make(
            get: function ($value, array $attributes) {
                return $this->dealSlots->groupBy('day')->map(function (Collection $slots, $day) {
                    $now = Carbon::now();
                    $date = $now->dayOfWeek === $day ? $now->toDateString() : $now->next($day)->toDateString();

                    return [
                        'date' => $date,
                        'available_seats' => $this->max_usage_per_day - UserDeal::query()->where('deal_id', $this->id)->whereJsonContains('reserve_slot->date', $date)->count(),
                        /** @phpstan-ignore-next-line  */
                        'slots' => $slots->map(function (DealSlot $slot) use ($date) {
                            return [
                                'from' => Carbon::parse($date.' '.$slot->from),
                                'to' => $slot->to < $slot->from
                                    ? Carbon::parse($date.' '.$slot->to)->addDay()->toDateTimeString()
                                    : Carbon::parse($date.' '.$slot->to)->toDateTimeString(),
                            ];
                        })->toArray(),
                    ];
                })->toArray();
            },
        );
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(Deal::class, 'user_deal')
            ->as('deal')
            ->withPivot([
                'reserved_at',
                'redeemed_at',
                'reuse_after',
                'status',
                'reserve_slot',
            ])
            ->using(UserDeal::class);
    }
}
