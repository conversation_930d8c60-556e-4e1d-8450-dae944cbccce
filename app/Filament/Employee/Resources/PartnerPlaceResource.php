<?php

namespace App\Filament\Employee\Resources;

use App\Enums\ApprovalStatus;
use App\Filament\Employee\Resources\PartnerPlaceResource\Pages;
use App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers\DealsRelationManager;
use App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers\ReelsRelationManager;
use App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers\UserDealsRelationManager;
use App\Models\PartnerLocation;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class PartnerPlaceResource extends Resource
{
    protected static ?string $model = PartnerLocation::class;

    protected static ?string $navigationIcon = 'heroicon-o-bookmark-square';

    protected static ?string $navigationLabel = 'Partner Places';

    public static function form(Form $form): Form
    {
        return \App\Filament\Resources\PartnerPlaceResource::form($form);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->color(fn ($record) => $record->trashed() ? 'gray' : null),

                TextColumn::make('partner.name')
                    ->searchable()
                    ->color(fn ($record) => $record->trashed() ? 'gray' : null),

                SpatieMediaLibraryImageColumn::make('avatar')
                    ->collection('avatar'),

                BadgeColumn::make('approval_status')
                    ->label('Status')
                    ->colors([
                        'gray' => ApprovalStatus::DRAFT,
                        'warning' => ApprovalStatus::PENDING,
                        'success' => ApprovalStatus::APPROVED,
                        'danger' => ApprovalStatus::REJECTED,
                    ])
                    ->icons([
                        'heroicon-o-document' => ApprovalStatus::DRAFT,
                        'heroicon-o-clock' => ApprovalStatus::PENDING,
                        'heroicon-o-check-circle' => ApprovalStatus::APPROVED,
                        'heroicon-o-x-circle' => ApprovalStatus::REJECTED,
                    ]),

                TextColumn::make('submitted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('reviewed_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->actions([
                EditAction::make()
                    ->visible(fn (PartnerLocation $record): bool => $record->canBeEdited()),
                Action::make('submit_for_approval')
                    ->label('Submit for Approval')
                    ->icon('heroicon-o-paper-airplane')
                    ->color('success')
                    ->visible(fn (PartnerLocation $record): bool => $record->isDraft() || $record->isRejected())
                    ->action(function (PartnerLocation $record): void {
                        $record->submitForApproval($record->getAttributes(), auth('employee')->id());
                        Notification::make()
                            ->title('Partner place submitted for approval')
                            ->success()
                            ->send();
                    }),
                Action::make('withdraw')
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->color('warning')
                    ->visible(fn (PartnerLocation $record): bool => $record->isPending())
                    ->action(function (PartnerLocation $record): void {
                        $record->withdraw();
                        Notification::make()
                            ->title('Partner place submission withdrawn')
                            ->success()
                            ->send();
                    }),
                DeleteAction::make()
                    ->visible(fn (PartnerLocation $record): bool => $record->canBeEdited()),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereHas('employees', fn (Builder $query) => $query->where('employee_id', auth('employee')->id()));
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPartnerPlaces::route('/'),
            'create' => Pages\CreatePartnerPlace::route('/create'),
            'edit' => Pages\EditPartnerPlace::route('/{record}/edit'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            DealsRelationManager::class,
            ReelsRelationManager::class,
            UserDealsRelationManager::class,
        ];
    }
}
