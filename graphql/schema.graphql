type Query
type Mutation

#import scalars.graphql

# User Module
#import Mutations/user.graphql
#import Mutations/deal.graphql
#import Mutations/collection.graphql
#import Mutations/fcm.graphql

#import Queries/user.graphql
#import Queries/partner.graphql
#import Queries/deal.graphql
#import Queries/reel.graphql
#import Queries/creator.graphql
#import Queries/like.graphql
#import Queries/collection.graphql

#import Types/user.graphql
#import Types/partner.graphql
#import Types/deal.graphql
#import Types/media.graphql
#import Types/reel.graphql
#import Types/creator.graphql
#import Types/like.graphql
#import Types/admin.graphql
#import Types/collection.graphql
