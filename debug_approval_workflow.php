<?php

// Debug script to test the approval workflow
// Run this with: php artisan tinker < debug_approval_workflow.php

use App\Enums\ApprovalStatus;
use App\Models\Deal;
use App\Models\Employee;
use App\Models\Admin;

echo "=== Testing Approval Workflow ===\n";

// Create test data
$employee = Employee::first() ?? Employee::factory()->create(['name' => 'Test Employee']);
$admin = Admin::first() ?? Admin::factory()->create(['name' => 'Test Admin']);

echo "Employee: {$employee->name} (ID: {$employee->id})\n";
echo "Admin: {$admin->name} (ID: {$admin->id})\n\n";

// Create an approved deal
$deal = Deal::factory()->create([
    'title' => 'Original Deal Title',
    'description' => 'Original Description',
    'max_saving' => 50,
    'approval_status' => ApprovalStatus::APPROVED,
]);

echo "1. Created approved deal:\n";
echo "   ID: {$deal->id}\n";
echo "   Title: '{$deal->title}'\n";
echo "   Status: {$deal->approval_status->value}\n";
echo "   Pending Data: " . ($deal->pending_data ? json_encode($deal->pending_data) : 'null') . "\n\n";

// Simulate staff editing the deal (this should store as pending data)
$newData = [
    'title' => 'Updated Deal Title by Staff',
    'description' => 'Updated Description by Staff',
    'max_saving' => 100,
];

echo "2. Staff editing deal with new data:\n";
foreach ($newData as $key => $value) {
    echo "   {$key}: '{$value}'\n";
}

// Manually simulate what happens when staff saves the form
$deal->update([
    'approval_status' => ApprovalStatus::PENDING,
    'pending_data' => $newData,
    'submitted_by' => $employee->id,
    'submitted_at' => now(),
    'reviewed_by' => null,
    'reviewed_at' => null,
    'rejection_reason' => null,
]);

$deal->refresh();

echo "\n3. After staff submission:\n";
echo "   Title (in DB): '{$deal->title}'\n";
echo "   Status: {$deal->approval_status->value}\n";
echo "   Pending Data: " . json_encode($deal->pending_data) . "\n";
echo "   Submitted By: {$deal->submitted_by}\n\n";

// Test the view changes logic
echo "4. Testing view changes logic:\n";
$originalData = $deal->getOriginalData();
$pendingData = $deal->pending_data ?? [];

echo "   Original title: '{$originalData['title']}'\n";
echo "   Pending title: '{$pendingData['title']}'\n";

$changes = [];
if (!empty($pendingData)) {
    foreach ($pendingData as $key => $newValue) {
        if (in_array($key, ['approval_status', 'pending_data', 'submitted_by', 'submitted_at', 'reviewed_by', 'reviewed_at', 'rejection_reason', 'created_at', 'updated_at', 'deleted_at', 'id'])) {
            continue;
        }
        
        $oldValue = $originalData[$key] ?? null;
        
        if ($oldValue != $newValue) {
            $changes[] = [
                'field' => ucfirst(str_replace('_', ' ', $key)),
                'old' => $oldValue,
                'new' => $newValue,
            ];
        }
    }
}

echo "   Changes detected: " . count($changes) . "\n";
foreach ($changes as $change) {
    echo "   - {$change['field']}: '{$change['old']}' → '{$change['new']}'\n";
}

echo "\n=== Test Complete ===\n";
