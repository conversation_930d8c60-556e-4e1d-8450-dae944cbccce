<?php

use App\Models\Deal;
use App\Models\Partner;
use App\Models\PartnerLocation;
use App\Models\Reel;
use App\Models\User;
use App\Models\UserDeal;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('deleting a partner cascades to delete partner_locations', function () {
    // Create a partner with locations
    /** @var Partner $partner */
    $partner = Partner::factory()->create();
    PartnerLocation::factory()->create(['partner_id' => $partner->id]);
    PartnerLocation::factory()->create(['partner_id' => $partner->id]);

    // Verify initial state
    expect(PartnerLocation::query()->count())->toBe(2);

    // Delete the partner
    $partner->delete();

    // Verify all partner locations were deleted
    expect(PartnerLocation::query()->count())->toBe(0);
});

test('deleting a location deletes any attached reels', function () {
    // Create a location and reels
    /** @var PartnerLocation $location */
    $location = PartnerLocation::factory()->create();
    /** @var Reel $reel1 */
    $reel1 = Reel::factory()->create();
    /** @var Reel $reel2 */
    $reel2 = Reel::factory()->create();

    // Attach reels to location
    $location->reels()->attach([$reel1->id, $reel2->id]);

    $reel1->refresh();

    // Verify initial state
    expect($location->reels()->count())->toBe(2);
    expect($reel1->locations()->count())->toBe(1);
    expect($reel2->locations()->count())->toBe(1);

    // Delete the location
    $location->delete();

    // Verify reels are detached but not deleted
    expect(Reel::query()->count())->toBe(0);
});

test('deleting a location cascades to delete related deals', function () {
    // Create a location with deals
    /** @var PartnerLocation $location */
    $location = PartnerLocation::factory()->create();
    $deal1 = Deal::factory()->create(['partner_location_id' => $location->id]);
    $deal2 = Deal::factory()->create(['partner_location_id' => $location->id]);

    // Verify initial state
    expect(Deal::query()->count())->toBe(2);

    // Delete the location
    $location->delete();

    // Verify all deals were deleted
    expect(Deal::query()->count())->toBe(0);
});

test('deleting a deal removes user-deal relationships', function () {
    // Create a deal with user relationships
    /** @var Deal $deal */
    $deal = Deal::factory()->create();
    /** @var User $user1 */
    $user1 = User::factory()->create();
    /** @var User $user2 */
    $user2 = User::factory()->create();

    // UserDeal with status ['upcoming', 'redeemable'] will not be deleted
    // Create user-deal relationships
    UserDeal::factory()->noShow()->create([
        'user_id' => $user1->id,
        'deal_id' => $deal->id,
    ]);

    UserDeal::factory()->redeemed()->create([
        'user_id' => $user2->id,
        'deal_id' => $deal->id,
    ]);

    // Verify initial state
    expect(UserDeal::query()->count())->toBe(2);
    \Pest\Laravel\assertDatabaseHas(User::class, ['id' => $user1->id]);
    \Pest\Laravel\assertDatabaseHas(User::class, ['id' => $user2->id]);

    // Delete the deal
    $deal->delete();

    // Verify all user-deal relationships were deleted but users remain
    expect(UserDeal::query()->count())->toBe(0);
    \Pest\Laravel\assertDatabaseHas(User::class, ['id' => $user1->id]);
    \Pest\Laravel\assertDatabaseHas(User::class, ['id' => $user2->id]);
});
