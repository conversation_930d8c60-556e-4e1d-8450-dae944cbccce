<?php

namespace App\Filament\Employee\Resources\DealResource\Pages;

use App\Enums\ApprovalStatus;
use App\Filament\Employee\Resources\DealResource;
use App\Models\Deal;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Log;

class EditDeal extends EditRecord
{
    protected static string $resource = DealResource::class;

    protected function getHeaderActions(): array
    {
        $actions = [];

        // Only show delete action if record can be edited
        if ($this->getRecord()->canBeEdited()) {
            $actions[] = Actions\DeleteAction::make();
        }

        // Show submit for approval action if record is draft or rejected
        if ($this->getRecord()->isDraft() || $this->getRecord()->isRejected()) {
            $actions[] = Actions\Action::make('submit_for_approval')
                ->label('Submit for Approval')
                ->icon('heroicon-o-paper-airplane')
                ->color('success')
                ->action(function (): void {
                    $this->submitForApproval();
                });
        }

        // Show withdraw action if record is pending
        if ($this->getRecord()->isPending()) {
            $actions[] = Actions\Action::make('withdraw')
                ->label('Withdraw Submission')
                ->icon('heroicon-o-arrow-uturn-left')
                ->color('warning')
                ->action(function (): void {
                    $this->getRecord()->withdraw();
                    Notification::make()
                        ->title('Deal submission withdrawn')
                        ->success()
                        ->send();
                    $this->redirect($this->getResource()::getUrl('edit', ['record' => $this->getRecord()]));
                });
        }

        return $actions;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        /** @var Deal $record */
        $record = $this->getRecord();

        // If record is pending, don't save directly
        if ($record->isPending()) {
            // This shouldn't happen as form should be disabled, but just in case
            return $record->getAttributes();
        }

        // For approved deals, when staff edit them, create a draft instead of applying changes directly
        if ($record->isApproved()) {
            // Check if there's already a pending draft
            $existingDraft = $record->pendingDraft;

            if ($existingDraft) {
                // Update existing draft
                $existingDraft->update($data);
                Log::info('Employee Edit Deal - Updated Existing Draft', [
                    'deal_id' => $record->id,
                    'draft_id' => $existingDraft->id,
                    'data' => $data,
                ]);
            } else {
                // Create new draft
                $draftData = array_merge($data, [
                    'original_deal_id' => $record->id,
                    'approval_status' => ApprovalStatus::PENDING,
                    'submitted_by' => auth('employee')->id(),
                    'submitted_at' => now(),
                ]);

                $draft = \App\Models\DealDraft::create($draftData);
                Log::info('Employee Edit Deal - Created New Draft', [
                    'deal_id' => $record->id,
                    'draft_id' => $draft->id,
                    'data' => $draftData,
                ]);
            }

            // Return original data to prevent direct changes to the deal
            return $record->getAttributes();
        }

        // For draft or rejected deals, allow normal editing but set status to draft
        if ($record->isDraft() || $record->isRejected()) {
            $data['approval_status'] = ApprovalStatus::DRAFT;
        }

        return $data;
    }

    protected function afterSave(): void
    {
        /** @var Deal $record */
        $record = $this->getRecord();

        // Check if there's a pending draft (means changes were submitted for approval)
        if ($record->pendingDraft) {
            Notification::make()
                ->title('Changes submitted for approval')
                ->body('Your changes have been automatically submitted for admin approval.')
                ->success()
                ->send();

            // Redirect to refresh the page and show the pending state
            $this->redirect($this->getResource()::getUrl('edit', ['record' => $record]));
        }
        // If the record was just saved and is now draft, show a message about submitting for approval
        elseif ($record->isDraft()) {
            Notification::make()
                ->title('Deal updated')
                ->body('Your changes have been saved as a draft. Submit for approval when ready.')
                ->success()
                ->send();
        }
    }

    protected function submitForApproval(): void
    {
        /** @var Deal $record */
        $record = $this->getRecord();

        // For draft or rejected deals, get current form data and submit for approval
        if ($record->isDraft() || $record->isRejected()) {
            // Get current form data
            $formData = $this->form->getState();

            // Remove approval-related fields from form data
            unset($formData['approval_status'], $formData['pending_data'], $formData['submitted_by'],
                $formData['submitted_at'], $formData['reviewed_by'], $formData['reviewed_at'],
                $formData['rejection_reason']);

            $record->submitForApproval($formData, auth('employee')->id());

            Notification::make()
                ->title('Deal submitted for approval')
                ->success()
                ->send();

            $this->redirect($this->getResource()::getUrl('edit', ['record' => $record]));
        }
    }

    public function isReadOnly(): bool
    {
        return $this->getRecord()->pendingDraft !== null;
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        /** @var Deal $record */
        $record = $this->getRecord();

        // If there's a pending draft, show the draft data in the form
        if ($record->pendingDraft) {
            $draftData = $record->pendingDraft->toArray();
            // Remove draft-specific fields
            unset($draftData['id'], $draftData['original_deal_id'], $draftData['approval_status'],
                $draftData['submitted_by'], $draftData['submitted_at'], $draftData['reviewed_by'],
                $draftData['reviewed_at'], $draftData['rejection_reason'], $draftData['created_at'],
                $draftData['updated_at']);

            return array_merge($data, $draftData);
        }

        return $data;
    }
}
