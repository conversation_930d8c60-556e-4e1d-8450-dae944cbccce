<div class="space-y-4">
    <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Submission Details</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <span class="font-medium text-gray-700">Submitted by:</span>
                <span class="text-gray-900">{{ $submittedBy ?? 'Unknown' }}</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">Submitted at:</span>
                <span class="text-gray-900">{{ $submittedAt ?? 'Unknown' }}</span>
            </div>
        </div>
    </div>

    @if (empty($changes))
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p class="text-blue-800">No field changes detected. This might be a new submission or the changes are in
                non-visible fields.</p>
        </div>
    @else
        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Proposed Changes</h3>
            </div>

            <div class="divide-y divide-gray-200">
                @foreach ($changes as $change)
                    <div class="p-4">
                        <div class="mb-2">
                            <span
                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ $change['field'] }}
                            </span>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-1">
                                <label class="text-sm font-medium text-gray-700">Current Value:</label>
                                <div class="p-3 bg-red-50 border border-red-200 rounded-md">
                                    <code class="text-sm text-red-800 break-all">
                                        {{ $change['old'] ?: '(empty)' }}
                                    </code>
                                </div>
                            </div>

                            <div class="space-y-1">
                                <label class="text-sm font-medium text-gray-700">Proposed Value:</label>
                                <div class="p-3 bg-green-50 border border-green-200 rounded-md">
                                    <code class="text-sm text-green-800 break-all">
                                        {{ $change['new'] ?: '(empty)' }}
                                    </code>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
</div>
