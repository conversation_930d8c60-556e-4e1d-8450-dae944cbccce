<?php

use App\Filament\Resources\PartnerResource\Pages\CreatePartner;
use App\Models\Admin;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

it('validates partner name length', function () {
    /** @var Admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    // Test with name exceeding 20 characters
    livewire(CreatePartner::class)// @phpstan-ignore-line
        ->fillForm([
            'name' => str_repeat('a', 21),
            'description' => 'Test description',
        ])
        ->call('create')
        ->assertHasFormErrors(['name']);

    // Test with valid name length
    livewire(CreatePartner::class)// @phpstan-ignore-line
        ->fillForm([
            'name' => str_repeat('a', 20),
            'description' => 'Test description',
        ])
        ->call('create')
        ->assertHasNoFormErrors();
});
