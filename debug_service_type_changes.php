<?php

// Debug script to test service type changes in view changes modal
// Run this with: php artisan tinker < debug_service_type_changes.php

use App\Enums\ApprovalStatus;
use App\Models\Deal;
use App\Models\Tag;
use App\Models\Employee;
use App\Enums\TagType;

echo "=== Testing Service Type Changes ===\n";

// Create test data
$employee = Employee::first() ?? Employee::factory()->create(['name' => 'Test Employee']);

// Get or create service types
$serviceType1 = Tag::where('type', TagType::SERVICE_OPTIONS->value)->first() 
    ?? Tag::create(['title' => 'Spa Services', 'type' => TagType::SERVICE_OPTIONS->value]);
$serviceType2 = Tag::where('type', TagType::SERVICE_OPTIONS->value)->skip(1)->first() 
    ?? Tag::create(['title' => 'Fitness Services', 'type' => TagType::SERVICE_OPTIONS->value]);

echo "Service Type 1: {$serviceType1->title} (ID: {$serviceType1->id})\n";
echo "Service Type 2: {$serviceType2->title} (ID: {$serviceType2->id})\n\n";

// Create an approved deal with service type 1
$deal = Deal::factory()->create([
    'title' => 'Test Deal for Service Type',
    'description' => 'Original Description',
    'max_saving' => 50,
    'service_type_id' => $serviceType1->id,
    'approval_status' => ApprovalStatus::APPROVED,
]);

echo "1. Created approved deal:\n";
echo "   ID: {$deal->id}\n";
echo "   Title: '{$deal->title}'\n";
echo "   Service Type ID: {$deal->service_type_id}\n";
echo "   Service Type Name: {$deal->service_type->title}\n";
echo "   Status: {$deal->approval_status->value}\n\n";

// Simulate staff changing the service type
$newData = [
    'title' => 'Updated Deal Title',
    'service_type_id' => $serviceType2->id,
    'max_saving' => 100,
];

echo "2. Staff changing service type:\n";
echo "   New service_type_id: {$newData['service_type_id']}\n";
echo "   New service type name: {$serviceType2->title}\n\n";

// Manually simulate what happens when staff saves the form
$deal->update([
    'approval_status' => ApprovalStatus::PENDING,
    'pending_data' => $newData,
    'submitted_by' => $employee->id,
    'submitted_at' => now(),
    'reviewed_by' => null,
    'reviewed_at' => null,
    'rejection_reason' => null,
]);

$deal->refresh();

echo "3. After staff submission:\n";
echo "   Current service_type_id in DB: {$deal->service_type_id}\n";
echo "   Current service type name: {$deal->service_type->title}\n";
echo "   Status: {$deal->approval_status->value}\n";
echo "   Pending Data: " . json_encode($deal->pending_data) . "\n\n";

// Test the view changes logic
echo "4. Testing view changes logic:\n";
$originalData = $deal->getOriginalData();
$pendingData = $deal->pending_data ?? [];

echo "   Original service_type_id: " . ($originalData['service_type_id'] ?? 'null') . "\n";
echo "   Pending service_type_id: " . ($pendingData['service_type_id'] ?? 'null') . "\n";

$changes = [];
if (!empty($pendingData)) {
    foreach ($pendingData as $key => $newValue) {
        if (in_array($key, ['approval_status', 'pending_data', 'submitted_by', 'submitted_at', 'reviewed_by', 'reviewed_at', 'rejection_reason', 'created_at', 'updated_at', 'deleted_at', 'id'])) {
            continue;
        }
        
        $oldValue = $originalData[$key] ?? null;
        
        echo "   Comparing {$key}: '{$oldValue}' vs '{$newValue}'\n";
        
        // Handle type conversion for proper comparison
        $oldValueForComparison = $oldValue;
        $newValueForComparison = $newValue;
        
        // Convert numeric strings to integers for comparison
        if (is_numeric($oldValue) && is_numeric($newValue)) {
            $oldValueForComparison = (int) $oldValue;
            $newValueForComparison = (int) $newValue;
            echo "     Converted to: {$oldValueForComparison} vs {$newValueForComparison}\n";
        }
        
        if ($oldValueForComparison != $newValueForComparison) {
            // Format values for display
            $formattedOld = $oldValue;
            $formattedNew = $newValue;
            
            if ($key === 'service_type_id') {
                $oldTag = Tag::find($oldValue);
                $newTag = Tag::find($newValue);
                $formattedOld = $oldTag ? $oldTag->title : "Service Type ID: {$oldValue}";
                $formattedNew = $newTag ? $newTag->title : "Service Type ID: {$newValue}";
            }
            
            $changes[] = [
                'field' => ucfirst(str_replace('_', ' ', $key)),
                'old' => $formattedOld,
                'new' => $formattedNew,
            ];
        }
    }
}

echo "\n5. Changes detected: " . count($changes) . "\n";
foreach ($changes as $change) {
    echo "   - {$change['field']}: '{$change['old']}' → '{$change['new']}'\n";
}

echo "\n=== Test Complete ===\n";
