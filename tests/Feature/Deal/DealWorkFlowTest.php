<?php

use App\Models\Deal;
use App\Models\DealSlot;
use App\Models\UserDeal;
use Illuminate\Support\Facades\Notification;

beforeEach(function () {
    Notification::fake();
});

test('basic deal work flow', function () {
    /** @var \App\Models\User $user */
    $user = \App\Models\User::factory()->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    /** @var Deal $deal */
    $deal = Deal::factory()
        ->has(DealSlot::factory()->state(fn (array $attributes) => [
            'from' => '00:00',
            'to' => '02:00',
            'day' => now()->addDay()->format('w'),
        ]))
        ->create([
            'reuse_limit_days' => 7,
            'max_usage_per_day' => 10,
        ]);

    graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal(
                    input: {id: $id, reserve_slot: {date: $date, slot: {from: $from, to: $to}}, myDealIdToRenew: null}
                ) {
                    myDeal {
                        id
                        status
                        reuse_after
                    }
                }
            }',
        [
            'id' => $deal->id,
            'date' => now()->copy()->addDay()->toDateString(),
            'from' => now()->addDay()->format('Y-m-d 00:00'),
            'to' => now()->addDay()->format('Y-m-d 02:00'),
        ]
    )
        ->assertJson([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'id' => UserDeal::latest('id')->value('id'),
                        'status' => 'UPCOMING',
                        'reuse_after' => now()
                            ->addDay() // for slot
                            ->addDays(7) // for reuse limit
                            ->toDateString().' 02:00:00',
                    ],
                ],
            ],
        ]);

    \Pest\Laravel\travelTo(\Carbon\Carbon::parse(now()->addDay()->toDateString().'00:01'));

    \Pest\Laravel\artisan(\App\Console\Commands\Deal\MarkDealsAsRedeemableCommand::class);

    /** @var UserDeal $userDeal */
    $userDeal = UserDeal::query()->latest('id')->first();

    expect($userDeal->status)
        ->toBe('redeemable');

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($myDealId: ID!) {
            redeemDeal (input: {
                myDealId: $myDealId
            }) {
                myDeal {
                    id
                    status
                    deal {
                        id
                    }
                    redeemed_at
                }
            }
        }
    ', [
        'myDealId' => $userDeal->id,
    ]);

    expect($response->json('data.redeemDeal.myDeal.status'))
        ->toBe('REDEEMED')
        ->and($userDeal->refresh()->status)
        ->toBe('redeemed');
});

test('noShow deal work flow', function () {
    /** @var \App\Models\User $user */
    $user = \App\Models\User::factory()->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    /** @var Deal $deal */
    $deal = Deal::factory()
        ->has(DealSlot::factory()->state(fn (array $attributes) => [
            'from' => '00:00',
            'to' => '02:00',
            'day' => now()->addDay()->format('w'),
        ]))
        ->create([
            'reuse_limit_days' => 7,
            'max_usage_per_day' => 10,
        ]);

    graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal(
                    input: {id: $id, reserve_slot: {date: $date, slot: {from: $from, to: $to}}, myDealIdToRenew: null}
                ) {
                    myDeal {
                        id
                        status
                        reuse_after
                    }
                }
            }',
        [
            'id' => $deal->id,
            'date' => now()->copy()->addDay()->toDateString(),
            'from' => now()->addDay()->format('Y-m-d 00:00'),
            'to' => now()->addDay()->format('Y-m-d 02:00'),
        ]
    )
        ->assertJson([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'id' => UserDeal::latest('id')->value('id'),
                        'status' => 'UPCOMING',
                        'reuse_after' => now()
                            ->addDay()// for slot to
                            ->addDays(7) // for reuse limit
                            ->toDateString().' 02:00:00',
                    ],
                ],
            ],
        ]);

    \Pest\Laravel\travelTo(\Carbon\Carbon::parse(now()->addDay()->toDateString().'00:01'));

    \Pest\Laravel\artisan(\App\Console\Commands\Deal\MarkDealsAsRedeemableCommand::class);

    /** @var UserDeal $userDeal */
    $userDeal = UserDeal::query()->latest('id')->first();

    expect($userDeal->status)
        ->toBe('redeemable');

    \Pest\Laravel\travelTo(now()->addDays(100));

    \Pest\Laravel\artisan(\App\Console\Commands\Deal\MarkDealsAsNoShowCommand::class);

    expect($userDeal->refresh()->status)
        ->toBe('no-show');
});
