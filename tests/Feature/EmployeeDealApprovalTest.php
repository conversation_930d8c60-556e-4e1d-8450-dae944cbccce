<?php

namespace Tests\Feature;

use App\Enums\ApprovalStatus;
use App\Filament\Employee\Resources\DealResource;
use App\Models\Admin;
use App\Models\Deal;
use App\Models\Employee;
use App\Models\Partner;
use App\Models\PartnerLocation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class EmployeeDealApprovalTest extends TestCase
{
    use RefreshDatabase;

    protected Employee $employee;
    protected Admin $admin;
    protected PartnerLocation $partnerLocation;
    protected Deal $deal;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->employee = Employee::factory()->create();
        $this->admin = Admin::factory()->create();
        
        $partner = Partner::factory()->create();
        $this->partnerLocation = PartnerLocation::factory()->create([
            'partner_id' => $partner->id,
            'approval_status' => ApprovalStatus::APPROVED,
        ]);
        
        // Associate employee with partner location
        $this->partnerLocation->employees()->attach($this->employee->id);
        
        $this->deal = Deal::factory()->create([
            'partner_location_id' => $this->partnerLocation->id,
            'approval_status' => ApprovalStatus::DRAFT,
        ]);
    }

    /** @test */
    public function employee_can_view_deals_list()
    {
        $this->actingAs($this->employee, 'employee');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertSuccessful()
            ->assertCanSeeTableRecords([$this->deal]);
    }

    /** @test */
    public function employee_can_create_new_deal()
    {
        $this->actingAs($this->employee, 'employee');

        $dealData = [
            'partner_location_id' => $this->partnerLocation->id,
            'deal_type' => 'TWO_FOR_ONE',
            'title' => 'Test Deal',
            'description' => 'Test Description',
            'max_saving' => 50,
            'max_usage_per_day' => 1,
            'reuse_limit_days' => 7,
        ];

        Livewire::test(DealResource\Pages\CreateDeal::class)
            ->fillForm($dealData)
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('deals', [
            'title' => 'Test Deal',
            'approval_status' => ApprovalStatus::DRAFT->value,
            'partner_location_id' => $this->partnerLocation->id,
        ]);
    }

    /** @test */
    public function employee_can_edit_draft_deal()
    {
        $this->actingAs($this->employee, 'employee');

        Livewire::test(DealResource\Pages\EditDeal::class, ['record' => $this->deal->id])
            ->assertFormSet([
                'title' => $this->deal->title,
                'description' => $this->deal->description,
            ])
            ->fillForm([
                'title' => 'Updated Deal Title',
                'description' => 'Updated Description',
            ])
            ->call('save')
            ->assertHasNoFormErrors();

        $this->deal->refresh();
        $this->assertEquals('Updated Deal Title', $this->deal->title);
        $this->assertEquals('Updated Description', $this->deal->description);
    }

    /** @test */
    public function employee_can_submit_deal_for_approval()
    {
        $this->actingAs($this->employee, 'employee');

        Livewire::test(DealResource\Pages\EditDeal::class, ['record' => $this->deal->id])
            ->call('submitForApproval');

        $this->deal->refresh();
        $this->assertTrue($this->deal->isPending());
        $this->assertEquals($this->employee->id, $this->deal->submitted_by);
        $this->assertNotNull($this->deal->submitted_at);
    }

    /** @test */
    public function employee_cannot_edit_pending_deal()
    {
        $this->deal->submitForApproval(['title' => 'Pending Title'], $this->employee->id);
        $this->actingAs($this->employee, 'employee');

        $component = Livewire::test(DealResource\Pages\EditDeal::class, ['record' => $this->deal->id]);
        
        $this->assertTrue($component->instance()->isReadOnly());
    }

    /** @test */
    public function employee_can_withdraw_pending_deal()
    {
        $this->deal->submitForApproval(['title' => 'Pending Title'], $this->employee->id);
        $this->actingAs($this->employee, 'employee');

        Livewire::test(DealResource\Pages\EditDeal::class, ['record' => $this->deal->id])
            ->assertActionExists('withdraw')
            ->callAction('withdraw');

        $this->deal->refresh();
        $this->assertTrue($this->deal->isDraft());
        $this->assertNull($this->deal->pending_data);
    }

    /** @test */
    public function employee_can_see_approval_status_in_table()
    {
        $this->actingAs($this->employee, 'employee');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertCanRenderTableColumn('approval_status');
    }

    /** @test */
    public function employee_can_only_see_deals_from_their_partner_locations()
    {
        // Create another partner location not associated with this employee
        $otherPartner = Partner::factory()->create();
        $otherPartnerLocation = PartnerLocation::factory()->create([
            'partner_id' => $otherPartner->id,
        ]);
        $otherDeal = Deal::factory()->create([
            'partner_location_id' => $otherPartnerLocation->id,
        ]);

        $this->actingAs($this->employee, 'employee');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertCanSeeTableRecords([$this->deal])
            ->assertCanNotSeeTableRecords([$otherDeal]);
    }

    /** @test */
    public function employee_sees_submit_for_approval_action_for_draft_deals()
    {
        $this->actingAs($this->employee, 'employee');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertTableActionDoesNotExist('submit_for_approval', $this->deal);

        // The action should be in the edit page, not the list page
        Livewire::test(DealResource\Pages\EditDeal::class, ['record' => $this->deal->id])
            ->assertActionExists('submit_for_approval');
    }

    /** @test */
    public function employee_sees_withdraw_action_for_pending_deals()
    {
        $this->deal->submitForApproval(['title' => 'Pending Title'], $this->employee->id);
        $this->actingAs($this->employee, 'employee');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertTableActionExists('withdraw', $this->deal);
    }

    /** @test */
    public function employee_cannot_delete_pending_deals()
    {
        $this->deal->submitForApproval(['title' => 'Pending Title'], $this->employee->id);
        $this->actingAs($this->employee, 'employee');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertTableActionHidden('delete', $this->deal);
    }

    /** @test */
    public function employee_cannot_edit_pending_deals_from_table()
    {
        $this->deal->submitForApproval(['title' => 'Pending Title'], $this->employee->id);
        $this->actingAs($this->employee, 'employee');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertTableActionHidden('edit', $this->deal);
    }
}
