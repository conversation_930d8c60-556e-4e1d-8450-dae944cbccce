<?php

namespace App\Filament\Employee\Resources\DealResource\Pages;

use App\Enums\ApprovalStatus;
use App\Filament\Employee\Resources\DealResource;
use App\Models\Deal;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditDeal extends EditRecord
{
    protected static string $resource = DealResource::class;

    protected function getHeaderActions(): array
    {
        $actions = [];

        // Only show delete action if record can be edited
        if ($this->getRecord()->canBeEdited()) {
            $actions[] = Actions\DeleteAction::make();
        }

        // Show submit for approval action if record is draft or rejected
        if ($this->getRecord()->isDraft() || $this->getRecord()->isRejected()) {
            $actions[] = Actions\Action::make('submit_for_approval')
                ->label('Submit for Approval')
                ->icon('heroicon-o-paper-airplane')
                ->color('success')
                ->action(function (): void {
                    $this->submitForApproval();
                });
        }

        // Show withdraw action if record is pending
        if ($this->getRecord()->isPending()) {
            $actions[] = Actions\Action::make('withdraw')
                ->label('Withdraw Submission')
                ->icon('heroicon-o-arrow-uturn-left')
                ->color('warning')
                ->action(function (): void {
                    $this->getRecord()->withdraw();
                    Notification::make()
                        ->title('Deal submission withdrawn')
                        ->success()
                        ->send();
                    $this->redirect($this->getResource()::getUrl('edit', ['record' => $this->getRecord()]));
                });
        }

        return $actions;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        /** @var Deal $record */
        $record = $this->getRecord();

        // If record is pending, don't save directly - store as pending data
        if ($record->isPending()) {
            // This shouldn't happen as form should be disabled, but just in case
            return $record->getAttributes();
        }

        // If record is approved, rejected, or draft, allow normal editing
        // but set status to draft to require re-approval
        if ($record->isApproved() || $record->isRejected()) {
            $data['approval_status'] = ApprovalStatus::DRAFT;
        }

        return $data;
    }

    protected function submitForApproval(): void
    {
        /** @var Deal $record */
        $record = $this->getRecord();

        // Get current form data
        $formData = $this->form->getState();

        // Remove approval-related fields from form data
        unset($formData['approval_status'], $formData['pending_data'], $formData['submitted_by'],
              $formData['submitted_at'], $formData['reviewed_by'], $formData['reviewed_at'],
              $formData['rejection_reason']);

        $record->submitForApproval($formData, auth('employee')->id());

        Notification::make()
            ->title('Deal submitted for approval')
            ->success()
            ->send();

        $this->redirect($this->getResource()::getUrl('edit', ['record' => $record]));
    }

    public function isReadOnly(): bool
    {
        return $this->getRecord()->isPending();
    }
}
