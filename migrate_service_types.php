<?php

// Migration script to move from many-to-many service types to single service_type_id
// Run this with: php artisan tinker < migrate_service_types.php

use App\Models\Deal;
use App\Models\Tag;
use App\Enums\TagType;

echo "=== Migrating Service Types from Many-to-Many to Single ===\n";

// Get all deals that have service options but no service_type_id
$deals = Deal::whereNull('service_type_id')
    ->whereHas('service_options')
    ->with('service_options')
    ->get();

echo "Found {$deals->count()} deals to migrate\n\n";

foreach ($deals as $deal) {
    echo "Deal ID: {$deal->id} - {$deal->title}\n";
    
    $serviceOptions = $deal->service_options;
    echo "  Current service options: " . $serviceOptions->pluck('title')->join(', ') . "\n";
    
    if ($serviceOptions->isNotEmpty()) {
        // Take the first service option as the primary one
        $primaryServiceType = $serviceOptions->first();
        
        // Update the deal with the service_type_id
        $deal->update(['service_type_id' => $primaryServiceType->id]);
        
        echo "  ✅ Set service_type_id to: {$primaryServiceType->title} (ID: {$primaryServiceType->id})\n";
        
        // Optionally, you can keep the many-to-many relationship for backward compatibility
        // or remove it if you want to clean up
        // $deal->service_options()->detach();
        // echo "  🧹 Removed old many-to-many relationships\n";
    } else {
        echo "  ⚠️  No service options found\n";
    }
    
    echo "\n";
}

echo "=== Migration Complete ===\n";

// Show summary
$totalDeals = Deal::count();
$dealsWithServiceType = Deal::whereNotNull('service_type_id')->count();
$dealsWithoutServiceType = $totalDeals - $dealsWithServiceType;

echo "Summary:\n";
echo "- Total deals: {$totalDeals}\n";
echo "- Deals with service_type_id: {$dealsWithServiceType}\n";
echo "- Deals without service_type_id: {$dealsWithoutServiceType}\n";

if ($dealsWithoutServiceType > 0) {
    echo "\n⚠️  Warning: {$dealsWithoutServiceType} deals still don't have a service_type_id\n";
    echo "You may need to manually assign service types to these deals.\n";
}
