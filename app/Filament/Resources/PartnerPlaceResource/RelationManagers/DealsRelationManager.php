<?php

namespace App\Filament\Resources\PartnerPlaceResource\RelationManagers;

use App\Filament\Resources\DealResource;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Table;

// Add this line

class DealsRelationManager extends RelationManager
{
    protected static string $relationship = 'deals';

    public function form(Form $form): Form
    {
        return DealResource::form($form)
            ->columns(1);
        // The ->disabled() state for trashed records is inherited from DealResource::form()
    }

    public function table(Table $table): Table
    {
        // DealResource::table() already includes TrashedFilter, conditional actions,
        // and column styling for soft deletes.
        return DealResource::table($table)
            ->headerActions([
                CreateAction::make(),
                // Add other header actions if needed
            ]);
        // Ensure actions from DealResource are preserved and work correctly in modal context
        // If DealResource::table() defines actions like Edit, Delete, ForceDelete, Restore,
        // they should be available here.
    }
}
