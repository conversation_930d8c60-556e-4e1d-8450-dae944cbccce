<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes; // Import SoftDeletes

class Tag extends Model
{
    /** @uses HasFactory<TagFactory> */
    use HasFactory, SoftDeletes; // Add SoftDeletes trait

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'deleted_at' => 'datetime', // Add this cast
        ];
    }

    public function toArray(): array
    {
        return [
            'id' => (string) $this->id,
            'title' => $this->title,
        ];
    }
}
