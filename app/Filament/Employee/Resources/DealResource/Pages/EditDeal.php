<?php

namespace App\Filament\Employee\Resources\DealResource\Pages;

use App\Enums\ApprovalStatus;
use App\Filament\Employee\Resources\DealResource;
use App\Models\Deal;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Log;

class EditDeal extends EditRecord
{
    protected static string $resource = DealResource::class;

    protected function getHeaderActions(): array
    {
        $actions = [];

        // Only show delete action if record can be edited
        if ($this->getRecord()->canBeEdited()) {
            $actions[] = Actions\DeleteAction::make();
        }

        // Show submit for approval action if record is draft or rejected
        if ($this->getRecord()->isDraft() || $this->getRecord()->isRejected()) {
            $actions[] = Actions\Action::make('submit_for_approval')
                ->label('Submit for Approval')
                ->icon('heroicon-o-paper-airplane')
                ->color('success')
                ->action(function (): void {
                    $this->submitForApproval();
                });
        }

        // Show withdraw action if record is pending
        if ($this->getRecord()->isPending()) {
            $actions[] = Actions\Action::make('withdraw')
                ->label('Withdraw Submission')
                ->icon('heroicon-o-arrow-uturn-left')
                ->color('warning')
                ->action(function (): void {
                    $this->getRecord()->withdraw();
                    Notification::make()
                        ->title('Deal submission withdrawn')
                        ->success()
                        ->send();
                    $this->redirect($this->getResource()::getUrl('edit', ['record' => $this->getRecord()]));
                });
        }

        return $actions;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        /** @var Deal $record */
        $record = $this->getRecord();

        // If record is pending, don't save directly
        if ($record->isPending()) {
            // This shouldn't happen as form should be disabled, but just in case
            return $record->getAttributes();
        }

        // For approved deals, when staff edit them, we need to store changes as pending
        if ($record->isApproved()) {
            // Store the changes as pending data instead of applying them directly
            $originalData = $record->getOriginalData();

            // Remove approval-related fields from form data
            $cleanData = $data;
            unset($cleanData['approval_status'], $cleanData['pending_data'], $cleanData['submitted_by'],
                  $cleanData['submitted_at'], $cleanData['reviewed_by'], $cleanData['reviewed_at'],
                  $cleanData['rejection_reason']);

            // Debug: Log what we're storing as pending data
            Log::info('Employee Edit Deal - Storing Pending Data', [
                'deal_id' => $record->id,
                'original_service_type_id' => $record->service_type_id,
                'new_service_type_id' => $cleanData['service_type_id'] ?? 'not set',
                'clean_data' => $cleanData,
            ]);

            // Store as pending data and set status to pending
            $record->update([
                'approval_status' => ApprovalStatus::PENDING,
                'pending_data' => $cleanData,
                'submitted_by' => auth('employee')->id(),
                'submitted_at' => now(),
                'reviewed_by' => null,
                'reviewed_at' => null,
                'rejection_reason' => null,
            ]);

            // Return original data to prevent direct changes
            return $originalData;
        }

        // For draft or rejected deals, allow normal editing but set status to draft
        if ($record->isDraft() || $record->isRejected()) {
            $data['approval_status'] = ApprovalStatus::DRAFT;
        }

        return $data;
    }

    protected function afterSave(): void
    {
        /** @var Deal $record */
        $record = $this->getRecord();

        // If the record is now pending, it means changes were automatically submitted for approval
        if ($record->isPending()) {
            Notification::make()
                ->title('Changes submitted for approval')
                ->body('Your changes have been automatically submitted for admin approval.')
                ->success()
                ->send();

            // Redirect to refresh the page and show the pending state
            $this->redirect($this->getResource()::getUrl('edit', ['record' => $record]));
        }
        // If the record was just saved and is now draft, show a message about submitting for approval
        elseif ($record->isDraft()) {
            Notification::make()
                ->title('Deal updated')
                ->body('Your changes have been saved as a draft. Submit for approval when ready.')
                ->success()
                ->send();
        }
    }

    protected function submitForApproval(): void
    {
        /** @var Deal $record */
        $record = $this->getRecord();

        // For draft or rejected deals, get current form data and submit for approval
        if ($record->isDraft() || $record->isRejected()) {
            // Get current form data
            $formData = $this->form->getState();

            // Remove approval-related fields from form data
            unset($formData['approval_status'], $formData['pending_data'], $formData['submitted_by'],
                  $formData['submitted_at'], $formData['reviewed_by'], $formData['reviewed_at'],
                  $formData['rejection_reason']);

            $record->submitForApproval($formData, auth('employee')->id());

            Notification::make()
                ->title('Deal submitted for approval')
                ->success()
                ->send();

            $this->redirect($this->getResource()::getUrl('edit', ['record' => $record]));
        }
    }

    public function isReadOnly(): bool
    {
        return $this->getRecord()->isPending();
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        /** @var Deal $record */
        $record = $this->getRecord();

        // If the record is pending, show the pending data in the form
        if ($record->isPending() && $record->pending_data) {
            return array_merge($data, $record->pending_data);
        }

        return $data;
    }
}
