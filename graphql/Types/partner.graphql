type Partner {
    id: ID!
    name: String!
    description: String
    email: String
    website: String
    instagram: String
    facebook: String
    twitter: String
    partner_places: [PartnerPlace]
        @hasMany(relation: "locations", type: PAGINATOR)
    #@cache(maxAge: 3600)
}

type PartnerPlace @model(class: "App\\Models\\PartnerLocation") {
    id: ID! @cacheKey
    name: String!
    address_line_1: String
    address_line_2: String
    city: String
    state: String
    postal_code: String
    country: String
    phone: String
    price_per_person: Float
    rates: PartnerPlaceRate!
    opening_hours: [PartnerPlaceOpeningHours!]!
    partner: Partner! @belongsTo(relation: "partner") #@cache(maxAge: 3600)
    tags: [Tag!] @belongsToMany(relation: "tags") #@cache(maxAge: 3600)
    images: [Media!]! @morphMany(relation: "images") #@cache(maxAge: 3600)
    menu: [Media] @morphMany(relation: "menu") #@cache(maxAge: 3600)
    location: GeoLocation!
    avatar: Media! @morphOne(relation: "avatar") #@cache(maxAge: 3600)
    meal_times: [Tag]!
    service_options: [Tag]!
    dietary: [Tag]!
    cuisine_types: [Tag]!
    ambiance: [Tag]!
    specialities: [Tag]!
    parking: [Tag]!
    cravings: [Tag]!
    area: Tag @first @where(key: "type", value: "area")
    retail_destination: Tag
        @first
        @where(key: "type", value: "retail_destination")

    menu_url: String
}

type GeoLocation {
    lat: Float
    lng: Float
}

type PartnerPlaceRate {
    reviews_count: Int
    google: String
}

type PartnerPlaceOpeningHours {
    day: Int
    from: DateTime
    to: DateTime
}

type Tag {
    id: ID! @cacheKey
    title: String!
}

extend type User {
    following_places: [PartnerPlace!]
        @belongsToMany(relation: "following_places", type: PAGINATOR)
}

extend type Reel {
    places: [PartnerPlace]!
        @belongsToMany(relation: "locations", type: PAGINATOR)
}

extend type Creator {
    places: [PartnerPlace!]!
        @paginate(resolver: "App\\GraphQL\\Types\\Creator\\Places")
    #@cache(maxAge: 7200)
    places_count: Int
        @field(resolver: "App\\GraphQL\\Types\\Creator\\PlacesCount")
    #@cache(maxAge: 3600)
}
