<?php

namespace App\Console\Commands;

use App\Models\Area;
use App\Models\PartnerLocation;
use App\Models\RetailDestination;
use App\Models\Tag;
use Illuminate\Console\Command;

class ConvertAreasAndDestinationsIntoTags extends Command
{
    protected $signature = 'app:convert-areas-and-destinations-into-tags';

    protected $description = 'Convert existing Areas and Retail Destinations into Tags and associate them with Partner Locations.';

    public function handle(): int
    {
        PartnerLocation::all()->each(function (PartnerLocation $partnerLocation) {

            if (! empty($partnerLocation->area_id)) {
                if ($area = Area::find($partnerLocation->area_id)) {
                    $partnerLocation->tags()->attach(Tag::where('title', $area->name)->first()?->id);
                }
            }
            if (! empty($partnerLocation->retail_destination_id)) {
                if ($retailDestination = RetailDestination::find($partnerLocation->retail_destination_id)) {
                    $partnerLocation->tags()->attach(Tag::where('title', $retailDestination->name)->first()?->id);
                }
            }
        });

        $this->components->success('Conversion completed successfully!');

        return self::SUCCESS;
    }
}
