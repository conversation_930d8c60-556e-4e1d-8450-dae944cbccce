<?php

namespace App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers;

use App\Enums\ApprovalStatus;
use App\Filament\Resources\ReelResource;
use App\Models\Reel;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ReelsRelationManager extends RelationManager
{
    protected static string $relationship = 'reels';

    public function form(Form $form): Form
    {
        return ReelResource::form($form);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('caption')
                    ->searchable()
                    ->limit(45)
                    ->label('Caption'),
                ImageColumn::make('thumbnail'),
                BadgeColumn::make('approval_status')
                    ->label('Status')
                    ->colors([
                        'gray' => ApprovalStatus::DRAFT,
                        'warning' => ApprovalStatus::PENDING,
                        'success' => ApprovalStatus::APPROVED,
                        'danger' => ApprovalStatus::REJECTED,
                    ])
                    ->icons([
                        'heroicon-o-document' => ApprovalStatus::DRAFT,
                        'heroicon-o-clock' => ApprovalStatus::PENDING,
                        'heroicon-o-check-circle' => ApprovalStatus::APPROVED,
                        'heroicon-o-x-circle' => ApprovalStatus::REJECTED,
                    ]),
                TextColumn::make('submitted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('reviewed_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->headerActions([
                Reel::getUploadReelsAction(),
            ])
            ->actions([
                EditAction::make()
                    ->visible(fn (Reel $record): bool => $record->canBeEdited()),
                Action::make('submit_for_approval')
                    ->label('Submit for Approval')
                    ->icon('heroicon-o-paper-airplane')
                    ->color('success')
                    ->visible(fn (Reel $record): bool => $record->isDraft() || $record->isRejected())
                    ->action(function (Reel $record): void {
                        $record->submitForApproval($record->getAttributes(), auth('employee')->id());
                        Notification::make()
                            ->title('Reel submitted for approval')
                            ->success()
                            ->send();
                    }),
                Action::make('withdraw')
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->color('warning')
                    ->visible(fn (Reel $record): bool => $record->isPending())
                    ->action(function (Reel $record): void {
                        $record->withdraw();
                        Notification::make()
                            ->title('Reel submission withdrawn')
                            ->success()
                            ->send();
                    }),
                DeleteAction::make()
                    ->visible(fn (Reel $record): bool => $record->canBeEdited()),
            ]);
    }
}
