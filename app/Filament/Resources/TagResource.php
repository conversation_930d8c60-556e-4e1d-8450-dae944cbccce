<?php

namespace App\Filament\Resources;

use App\Enums\TagType;
use App\Filament\Resources\TagResource\Pages;
use App\Models\Tag;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Str; // Ensure TextColumn is imported if not already fully qualified later

class TagResource extends Resource
{
    protected static ?string $model = Tag::class;

    protected static ?string $navigationIcon = 'heroicon-o-tag';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->live(true)
                    ->afterStateUpdated(fn (?string $state, Forms\Set $set) => $set('slug', Str::slug($state)))
                    ->maxLength(255),

                Forms\Components\TextInput::make('slug')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255),

                Forms\Components\Select::make('type')
                    ->options(TagType::class)
                    ->searchable()
                    ->preload()
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->searchable()
                    ->color(fn ($record) => $record->trashed() ? 'gray' : null), // Dim if trashed

                TextColumn::make('type')
                    ->searchable()
                    ->color(fn ($record) => $record->trashed() ? 'gray' : null), // Dim if trashed

                TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(), // Removed ->default(null)
            ])
            ->actions([
                Tables\Actions\EditAction::make()->hidden(fn ($record) => $record->trashed()),
                Tables\Actions\DeleteAction::make()->hidden(fn ($record) => $record->trashed()),
                Tables\Actions\ForceDeleteAction::make()->visible(fn ($record) => $record->trashed()),
                Tables\Actions\RestoreAction::make()->visible(fn ($record) => $record->trashed()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(), // Removed problematic hidden() condition
                    Tables\Actions\ForceDeleteBulkAction::make(), // Typically shown when TrashedFilter is active
                    Tables\Actions\RestoreBulkAction::make(),   // Typically shown when TrashedFilter is active
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTags::route('/'),
        ];
    }
}
