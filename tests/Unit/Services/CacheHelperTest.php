<?php

use App\Services\CacheHelper;

uses(Tests\TestCase::class);

it('can clear field cache', function () {
    $cacheHelper = app(CacheHelper::class);

    // Test that the method runs without errors
    $cacheHelper->clearFieldCache('User', 123, 'profile');

    expect(true)->toBeTrue(); // Basic assertion to ensure no exceptions
});

it('can clear type cache', function () {
    $cacheHelper = app(CacheHelper::class);

    // Test that the method runs without errors
    $cacheHelper->clearTypeCache('User', 123);

    expect(true)->toBeTrue(); // Basic assertion to ensure no exceptions
});

it('can clear cache by argument', function () {
    $cacheHelper = app(CacheHelper::class);
    $args = ['input' => ['id' => 123]];

    // Test that the method runs without errors
    $cacheHelper->clearCacheByArgument('User', 'input.id', $args, 'profile');

    expect(true)->toBeTrue(); // Basic assertion to ensure no exceptions
});

it('can clear cache by result field', function () {
    $cacheHelper = app(CacheHelper::class);
    $result = ['user' => ['id' => 123]];

    // Test that the method runs without errors
    $cacheHelper->clearCacheByResultField('User', 'user.id', $result, 'profile');

    expect(true)->toBeTrue(); // Basic assertion to ensure no exceptions
});

it('can clear cache manually', function () {
    $cacheHelper = app(CacheHelper::class);
    $args = ['input' => ['id' => 123]];

    // Test the main clearCache method
    $cacheHelper->clearCache(
        type: 'User',
        idSource: ['argument' => 'input.id'],
        field: 'profile',
        args: $args
    );

    expect(true)->toBeTrue(); // Basic assertion to ensure no exceptions
});
