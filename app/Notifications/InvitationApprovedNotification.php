<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class InvitationApprovedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct()
    {
        //
    }

    public function via($notifiable): array
    {
        return ['database', FcmChannel::class];
    }

    public function toFcm($notifiable): FcmMessage
    {
        return new FcmMessage(notification: new FcmNotification(
            title: 'You\'re In! 🎉',
            body: 'Welcome to Conari. Explore curated dining spots now.',
        ))
            ->data([
                'type' => 'invitation_approved',
                'user_id' => (string) $notifiable->id,
                'deep_link' => 'conari://home',
                'overlay_message' => 'Welcome to Conari 👋 You\'re now an Insider. Start exploring.',
                'show_overlay' => 'true',
            ])
            ->custom([
                'android' => [
                    'notification' => [
                        'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                        'color' => '#7569F3',
                        'sound' => 'notification',
                        'icon' => 'notification_icon',
                        'channel_id' => 'welcome',
                    ],
                    'fcm_options' => [
                        'analytics_label' => 'invitation_approved',
                    ],
                ],
                'apns' => [
                    'payload' => [
                        'aps' => [
                            'category' => 'INVITATION_APPROVED',
                            'sound' => 'default',
                        ],
                    ],
                    'fcm_options' => [
                        'analytics_label' => 'invitation_approved_ios',
                    ],
                ],
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'invitation_approved',
            'title' => 'You\'re In! 🎉',
            'message' => 'Welcome to Conari. Explore curated dining spots now.',
            'user_id' => $notifiable->id,
            'deep_link' => 'conari://home',
            'overlay_message' => 'Welcome to Conari 👋 You\'re now an Insider. Start exploring.',
            'show_overlay' => true,
        ];
    }
}
