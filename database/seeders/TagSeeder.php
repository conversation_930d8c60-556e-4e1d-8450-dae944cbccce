<?php

namespace Database\Seeders;

use App\Enums\TagType;
use App\Models\Area;
use App\Models\RetailDestination;
use App\Models\Tag;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class TagSeeder extends Seeder
{
    public function run(): void
    {
        $tags = [
            TagType::MEAL_TIMES->value => ['Breakfast', 'Lunch', 'Dinner', 'All Day Breakfast', 'Business Lunch', 'Brunch'],
            TagType::DIETARY->value => ['Vegan', 'Vegetarian', 'Gluten-Free', 'Dairy-Free', 'Healthy'],
            TagType::AMBIANCE->value => ['Outdoors', 'Rooftop', 'Beachfront', 'Family-Friendly', 'Romantic', 'Casual', 'Cozy', 'Modern', 'Live Music', 'Good for groups', 'Laptop-Friendly', 'Pets friendly', 'Good for kids', 'Sports Viewing', 'Kids activities / Kids Area'],
            TagType::SPECIALITIES->value => ['Bakery', 'Casual Dining', 'Specialty Coffee', 'Buffet', 'Fine Dining', 'Fast Food', 'Street Food', 'Food Truck', 'Hotel Restaurant', 'Board games', 'Dessert Shop'],
            TagType::CUISINE_TYPES->value => ['Arabic', 'Italian', 'Japanese', 'Chinese', 'Indian', 'American', 'Mexican', 'Thai', 'Mediterranean', 'French', 'Greek', 'Turkish', 'Lebanese', 'Egyptian', 'Yemeni', 'Korean', 'Vietnamese', 'Spanish', 'Brazilian', 'Persian', 'African', 'Caribbean', 'Scandinavian', 'International'],
            TagType::SERVICE_OPTIONS->value => ['Kerbside pickup', 'Drive-thru', 'Delivery', 'Takeaway', 'Dine-in'],
            TagType::PARKING->value => ['Free Parking', 'Paid Parking Lot', 'Paid Street Parking', 'Free Valet Parking', 'Paid Valet Parking'],
            TagType::AREA->value => Area::pluck('name')->all(),
            TagType::RETAIL_DESTINATION->value => RetailDestination::pluck('name')->all(),
        ];

        foreach ($tags as $type => $titles) {
            foreach ($titles as $title) {
                Tag::firstOrCreate([
                    'title' => $title,
                    'slug' => Str::slug($title),
                    'type' => $type,
                ]);
            }
        }
    }
}
