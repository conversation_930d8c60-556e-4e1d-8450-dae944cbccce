<?php

use App\Models\User;
use Illuminate\Support\Facades\Hash;

$mutation = <<<'GRAPHQL'
mutation addFCMToken($token: String!) {
  addFCMToken(token: $token) {
    id
    fcm_token
  }
}
GRAPHQL;

test('unauthenticated user cannot update fcm token', function () use ($mutation) {
    $response = graphQl($mutation, ['token' => 'test-fcm-token']);
    expect($response->json('errors.0.message'))->toBe('Unauthenticated.');
});

test('authenticated user can update their fcm token', function () use ($mutation) {
    $user = User::factory()->create([
        'password' => Hash::make('password'),
    ]);
    $token = $user->createToken('test-token')->plainTextToken;
    $response = graphQl(
        $mutation,
        ['token' => 'test-fcm-token'],
        [],
        ['Authorization' => 'Bearer '.$token]
    );
    expect($response->json('data.addFCMToken.fcm_token'))->toBe('test-fcm-token');
});

test('fcm token cannot be empty', function () use ($mutation) {
    $user = User::factory()->create([
        'password' => Hash::make('password'),
    ]);
    $token = $user->createToken('test-token')->plainTextToken;
    $response = graphQl(
        $mutation,
        ['token' => ''],
        [],
        ['Authorization' => 'Bearer '.$token]
    );
    expect($response->json('errors.0.message'))->toContain('Variable "$token" of non-null type "String!" must not be null.');
});

test('fcm token must be a string', function () use ($mutation) {
    $user = User::factory()->create([
        'password' => Hash::make('password'),
    ]);
    $token = $user->createToken('test-token')->plainTextToken;
    $response = graphQl(
        $mutation,
        ['token' => 123],
        [],
        ['Authorization' => 'Bearer '.$token]
    );
    expect($response->json('errors.0.message'))->toContain('Variable "$token" got invalid value 123; String cannot represent a non string value: 123');
});

test('user can update their fcm token multiple times', function () use ($mutation) {
    $user = User::factory()->create([
        'password' => Hash::make('password'),
    ]);
    $token = $user->createToken('test-token')->plainTextToken;
    // First update
    $response1 = graphQl(
        $mutation,
        ['token' => 'first-fcm-token'],
        [],
        ['Authorization' => 'Bearer '.$token]
    );
    expect($response1->json('data.addFCMToken.fcm_token'))->toBe('first-fcm-token');
    // Second update
    $response2 = graphQl(
        $mutation,
        ['token' => 'second-fcm-token'],
        [],
        ['Authorization' => 'Bearer '.$token]
    );
    expect($response2->json('data.addFCMToken.fcm_token'))->toBe('second-fcm-token');
});
