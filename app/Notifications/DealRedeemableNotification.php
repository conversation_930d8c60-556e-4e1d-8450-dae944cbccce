<?php

namespace App\Notifications;

use App\Models\Deal;
use App\Models\Partner;
use App\Models\UserDeal;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class DealRedeemableNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        private readonly UserDeal $userDeal
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via($notifiable): array
    {
        return ['database', FcmChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->line('The introduction to the notification.')
            ->action('Notification Action', url('/'))
            ->line('Thank you for using our application!');
    }

    public function toFcm($notifiable): FcmMessage
    {
        /** @var Deal $deal */
        $deal = $this->userDeal->deal;
        /** @var Partner $partner */
        $partner = $deal->partner_place;

        return (new FcmMessage(notification: new FcmNotification(
            title: 'Your Deal is Now Active! 🎉',
            body: "Great news! Your reserved deal at {$partner->name} is now active. Redeem it before it expires!",
        )))
            ->data([
                'type' => 'deal_redeemable',
                'deal_id' => (string) $deal->id,
                'user_deal_id' => (string) $this->userDeal->id,
                'partner_name' => $partner->name,
                'deep_link' => "conari://deals/{$deal->id}",
            ])
            ->custom([
                'android' => [
                    'notification' => [
                        'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                        'color' => '#4CAF50',
                        'sound' => 'notification',
                        'icon' => 'notification_icon',
                        'channel_id' => 'deals',
                    ],
                    'fcm_options' => [
                        'analytics_label' => 'deal_redeemable',
                    ],
                ],
                'apns' => [
                    'payload' => [
                        'aps' => [
                            'category' => 'DEAL_REDEEMABLE',
                            'sound' => 'default',
                        ],
                    ],
                    'fcm_options' => [
                        'analytics_label' => 'deal_redeemable_ios',
                    ],
                ],
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray($notifiable): array
    {
        /** @var Deal $deal */
        $deal = $this->userDeal->deal;
        /** @var Partner $partner */
        $partner = $deal->partner_place;

        return [
            'type' => 'deal_redeemable',
            'title' => 'Your Deal is Now Active! 🎉',
            'body' => "Great news! Your reserved deal at {$partner->name} is now active. Redeem it before it expires!",
            'deal_id' => $deal->id,
            'user_deal_id' => $this->userDeal->id,
            'partner_name' => $partner->name,
            'deep_link' => "conari://deals/{$deal->id}",
        ];
    }
}
