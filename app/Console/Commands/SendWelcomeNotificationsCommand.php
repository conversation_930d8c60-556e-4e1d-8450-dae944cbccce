<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Notifications\WelcomeAboardNotification;
use Illuminate\Console\Command;

class SendWelcomeNotificationsCommand extends Command
{
    protected $signature = 'notifications:send-welcome';

    protected $description = 'Send welcome notifications to users with FCM tokens';

    public function handle(): int
    {
        $users = User::whereNotNull('fcm_token')->get();

        $this->info("Found {$users->count()} users with FCM tokens.");

        $bar = $this->output->createProgressBar($users->count());
        $bar->start();

        foreach ($users as $user) {
            $user->notify(new WelcomeAboardNotification);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('Welcome notifications sent successfully!');

        return self::SUCCESS;
    }
}
