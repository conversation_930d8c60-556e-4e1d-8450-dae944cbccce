<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CreatorResource\Pages;
use App\Filament\Resources\CreatorResource\RelationManagers\ReelsRelationManager;
use App\Models\Creator;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;

class CreatorResource extends Resource
{
    protected static ?string $model = Creator::class;

    protected static ?string $slug = 'creators';

    protected static ?string $navigationIcon = 'heroicon-o-user-plus';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('user_id')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),

                TextInput::make('name')
                    ->required(),

                TextInput::make('username')
                    ->unique(ignoreRecord: true)
                    ->required(),

                TextInput::make('tiktok_url')
                    ->url(),

                TextInput::make('instagram_url')
                    ->url(),

                Textarea::make('bio')
                    ->required(),

                SpatieMediaLibraryFileUpload::make('avatar')
                    ->collection('avatar')
                    ->imageEditor()
                    ->downloadable()
                    ->avatar()
                    ->image(),
            ])
            ->disabled(fn (?Creator $record): bool => $record?->trashed() ?? false); // Disable form if record is trashed
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->color(fn ($record) => $record->trashed() ? 'gray' : null),

                TextColumn::make('user.name')
                    ->searchable()
                    ->sortable()
                    ->color(fn ($record) => $record->trashed() ? 'gray' : null),

                TextColumn::make('bio')
                    ->color(fn ($record) => $record->trashed() ? 'gray' : null),

                TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->actions([
                EditAction::make()->hidden(fn ($record) => $record->trashed()),
                DeleteAction::make()->hidden(fn ($record) => $record->trashed()),
                ForceDeleteAction::make()->visible(fn ($record) => $record->trashed()),
                RestoreAction::make()->visible(fn ($record) => $record->trashed()),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ])
            ->recordUrl(
                fn (?Creator $record): ?string => $record?->trashed() ? null : Pages\EditCreator::getUrl(['record' => $record])
            );
    }

    public static function getRelations(): array
    {
        return [
            ReelsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCreators::route('/'),
            'create' => Pages\CreateCreator::route('/create'),
            'edit' => Pages\EditCreator::route('/{record}/edit'),
        ];
    }
}
