<?php

use App\Console\Commands\SendWelcomeNotificationsCommand;
use App\Models\User;
use App\Notifications\WelcomeAboardNotification;
use Illuminate\Support\Facades\Notification;

use function Pest\Laravel\artisan;

beforeEach(function () {
    Notification::fake();
});

test('it sends welcome notifications to users with fcm tokens', function () {
    // Create users with and without FCM tokens
    $userWithToken = User::factory()->create(['fcm_token' => 'test-token-1']);
    $userWithoutToken = User::factory()->create(['fcm_token' => null]);
    $anotherUserWithToken = User::factory()->create(['fcm_token' => 'test-token-2']);

    // Run the command
    artisan(SendWelcomeNotificationsCommand::class);

    // Assert notifications were sent only to users with FCM tokens
    Notification::assertSentTo(
        [$userWithToken, $anotherUserWithToken],
        WelcomeAboardNotification::class
    );

    Notification::assertNotSentTo(
        [$userWithoutToken],
        WelcomeAboardNotification::class
    );
});
