<?php

declare(strict_types=1);

use App\Models\Partner;
use App\Models\PartnerLocation;

describe('Partner Queries', function () {
    test('fetch a single partner', function () {
        /** @var Partner */
        $partner = Partner::factory()->create();

        $response = graphQL(/** @lang GraphQL */ '
            query ($id: ID!) {
                partner(id: $id) {
                    id
                    name
                    description
                    email
                    website
                    instagram
                    facebook
                    twitter
                    partner_places(first: 10, page: 1) {
                        data {
                            id
                            name
                        }
                    }
                }
            }
        ', [
            'id' => $partner->id,
        ]);

        $response->assertJsonStructure([
            'data' => [
                'partner' => [
                    'id',
                    'name',
                    'description',
                    'email',
                    'website',
                    'instagram',
                    'facebook',
                    'twitter',
                    'partner_places' => [
                        'data' => [
                            '*' => [
                                'id',
                                'name',
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    });

    test('fetch all partners with pagination', function () {
        Partner::factory()->count(15)->create();

        $response = graphQL(/** @lang GraphQL */ '
            query ($first: Int!, $page: Int!) {
                partners(first: $first, page: $page) {
                    data {
                        id
                        name
                    }
                    paginatorInfo {
                        currentPage
                        lastPage
                        total
                    }
                }
            }
        ', [
            'first' => 10,
            'page' => 1,
        ]);

        $response->assertJsonStructure([
            'data' => [
                'partners' => [
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                        ],
                    ],
                    'paginatorInfo' => [
                        'currentPage',
                        'lastPage',
                        'total',
                    ],
                ],
            ],
        ]);
    });

    test('fetch partner with invalid ID', function () {
        $response = graphQL(/** @lang GraphQL */ '
            query {
                partner(id: "invalid") {
                    id
                }
            }
        ');

        $response->assertGraphQLErrorMessage('Validation failed for the field [partner].');
    });

    test('fetch partner places', function () {
        /** @var PartnerLocation */
        $partnerPlace = PartnerLocation::factory()->create();

        $partnerPlace->addMedia(\Illuminate\Http\UploadedFile::fake()->image('menu1.jpg'))->toMediaCollection('menu');
        $partnerPlace->addMedia(\Illuminate\Http\UploadedFile::fake()->image('menu2.jpg'))->toMediaCollection('menu');

        $response = graphQL(/** @lang GraphQL */ '
            query ($id: ID!) {
                partnerPlace(id: $id) {
                    id
                    name
                    address_line_1
                    city
                    state
                    postal_code
                    country
                    phone
                    price_per_person
                    menu {
                        id
                        full_url
                    }
                    rates {
                        reviews_count
                        google
                    }
                    opening_hours {
                        day
                        from
                        to
                    }
                    partner {
                        id
                        name
                    }
                    tags {
                        id
                        title
                    }
                    images {
                        id
                        full_url
                    }
                    location {
                        lat
                        lng
                    }
                }
            }
        ', [
            'id' => $partnerPlace->id,
        ]);

        $response->assertJsonStructure([
            'data' => [
                'partnerPlace' => [
                    'id',
                    'name',
                    'address_line_1',
                    'city',
                    'state',
                    'postal_code',
                    'country',
                    'phone',
                    'price_per_person',
                    'rates' => [
                        'reviews_count',
                        'google',
                    ],
                    'opening_hours' => [
                        '*' => [
                            'day',
                            'from',
                            'to',
                        ],
                    ],
                    'partner' => [
                        'id',
                        'name',
                    ],
                    'tags' => [
                        '*' => [
                            'id',
                            'title',
                        ],
                    ],
                    'images' => [
                        '*' => [
                            'id',
                            'full_url',
                        ],
                    ],
                    'menu' => [
                        '*' => [
                            'id',
                            'full_url',
                        ],
                    ],
                    'location' => [
                        'lat',
                        'lng',
                    ],
                ],
            ],
        ]);
    });

    test('fetch all partner places with pagination', function () {
        PartnerLocation::factory()->count(15)->create();

        $response = graphQL(/** @lang GraphQL */ '
                query ($first: Int!, $page: Int!) {
                    partnerPlaces(first: $first, page: $page) {
                        data {
                            id
                            name
                            partner {
                                id
                                name
                            }
                        }
                        paginatorInfo {
                            currentPage
                            lastPage
                            total
                        }
                    }
                }
            ', [
            'first' => 10,
            'page' => 1,
        ]);

        $response->assertJsonStructure([
            'data' => [
                'partnerPlaces' => [
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'partner' => [
                                'id',
                                'name',
                            ],
                        ],
                    ],
                    'paginatorInfo' => [
                        'currentPage',
                        'lastPage',
                        'total',
                    ],
                ],
            ],
        ]);
    });

    test('fetch partner place with invalid ID', function () {
        $response = graphQL(/** @lang GraphQL */ '
                query {
                    partnerPlace(id: "invalid") {
                        id
                    }
                }
            ');

        $response->assertGraphQLErrorMessage('Validation failed for the field [partnerPlace].');
    });

    test('fetch partner place with cache', function () {
        /** @var PartnerLocation $partnerPlace */
        $partnerPlace = PartnerLocation::factory()->create();

        $response = graphQL(/** @lang GraphQL */ '
                query ($id: ID!) {
                    partnerPlace(id: $id) {
                        id
                    }
                }
            ', ['id' => $partnerPlace->id]);

        expect((int) $response->json('data.partnerPlace.id'))
            ->toBe($partnerPlace->id)
            ->and(\Illuminate\Support\Facades\Cache::get("lighthouse:Query::partnerPlace:id:$partnerPlace->id"))
            ->not->toBeNull();

        \Pest\Laravel\travelTo(now()->addMinutes(61));

        expect(\Illuminate\Support\Facades\Cache::get("lighthouse:Query::partnerPlace:id:$partnerPlace->id"))
            ->toBeEmpty();
    })->group('cache')->skip('Disable cache test for now');

    test('fetch partner places with invalid pagination parameters', function () {
        $response = graphQL(/** @lang GraphQL */ '
                query {
                    partnerPlaces(first: 0, page: 0) {
                        data {
                            id
                        }
                    }
                }
            ');

        // Since @paginate doesn't validate parameters, we expect a successful response
        $response->assertJsonStructure([
            'data' => [
                'partnerPlaces' => [
                    'data' => [
                        '*' => [
                            'id',
                        ],
                    ],
                ],
            ],
        ]);
    });

    test('fetch partner place with reels filters out creator reels', function () {
        /** @var PartnerLocation $partnerPlace */
        $partnerPlace = PartnerLocation::factory()->create();

        // Create a creator
        /** @var \App\Models\Creator $creator */
        $creator = \App\Models\Creator::factory()->create();

        // Create 3 reels
        /** @var \App\Models\Reel $reel1 */
        $reel1 = \App\Models\Reel::factory()->create([
            'creatable_type' => \App\Models\Creator::class,
            'creatable_id' => $creator->id,
        ]);
        /** @var \App\Models\Reel $reel2 */
        $reel2 = \App\Models\Reel::factory()->create([
            'creatable_type' => \App\Models\Creator::class,
            'creatable_id' => $creator->id,
        ]);
        /** @var \App\Models\Reel $reel3 */
        $reel3 = \App\Models\Reel::factory()->create();

        // Attach all reels to the partner place
        $partnerPlace->reels()->attach([$reel1->id, $reel2->id, $reel3->id]);

        $response = graphQL(/** @lang GraphQL */ '
            query ($id: ID!) {
                partnerPlace(id: $id) {
                    id
                    name
                    reels(first: 10) {
                        data {
                            id
                            caption
                        }
                        paginatorInfo {
                            total
                        }
                    }
                }
            }
        ', [
            'id' => $partnerPlace->id,
        ]);

        $response->assertJsonStructure([
            'data' => [
                'partnerPlace' => [
                    'id',
                    'name',
                    'reels' => [
                        'data' => [
                            '*' => [
                                'id',
                                'caption',
                            ],
                        ],
                        'paginatorInfo' => [
                            'total',
                        ],
                    ],
                ],
            ],
        ]);

        // Assert that only one reel is returned (the one without a creator)
        $responseData = $response->json('data.partnerPlace.reels');
        expect($responseData['paginatorInfo']['total'])->toBe(1);
        expect($responseData['data'][0]['id'])->toBe((string) $reel3->id);
    });

    test('fetch partner place', function () {
        /** @var PartnerLocation $partnerPlace */
        $partnerPlace = PartnerLocation::factory()->withRetailDestination()->withArea()->create();

        $response = graphQL(/** @lang GraphQL */ '
            query ($id: ID!) {
                partnerPlace(id: $id) {
                    id
                    name
                    area {
                        title
                    }
                    retail_destination {
                        title
                    }
                }
            }
        ', [
            'id' => $partnerPlace->id,
        ]);

        $response->assertJsonStructure([
            'data' => [
                'partnerPlace' => [
                    'id',
                    'name',
                    'area' => [
                        'title',
                    ],
                    'retail_destination' => [
                        'title',
                    ],
                ],
            ],
        ]);

        expect($response->json('data.partnerPlace.area.title'))->toBe($partnerPlace->area->first()->title);
        expect($response->json('data.partnerPlace.retail_destination.title'))->toBe($partnerPlace->retailDestination->first()->title);
    });

});
