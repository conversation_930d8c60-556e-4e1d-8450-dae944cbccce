<?php

namespace App\Forms\Components;

use App\Models\Tag;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Fieldset;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Model;

class PartnerPlaceTagsInput extends Fieldset
{
    public ?string $type;

    public ?int $maxItems = null;

    public function type(string $type): static
    {
        return $this->make($type)
            ->label($this->label)
            ->columnSpan(1)
            ->schema(fn ($livewire, ?Model $record = null) => [
                CheckboxList::make($type)
                    ->hiddenLabel()
                    ->bulkToggleable()
                    ->when($this->maxItems, fn (Component $component) => $component->maxItems($this->maxItems))
                    ->when(
                        ! $livewire instanceof ListRecords,
                        fn (Component $component) => $component->afterStateHydrated(fn (Component $component) => $component->state($record ? $record->{$type}->pluck('id')->toArray() : []))->options(Tag::query()->where('type', $type)->pluck('title', 'id')),
                        fn (Component $component) => $component->options(Tag::query()->where('type', $type)->pluck('title', 'id'))
                    )
                    ->columns(3)
                    ->columnSpanFull(),
            ]);
    }

    public function maxItems(int $maxItems): static
    {
        $this->maxItems = $maxItems;

        return $this;
    }
}
