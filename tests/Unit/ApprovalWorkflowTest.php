<?php

namespace Tests\Unit;

use App\Enums\ApprovalStatus;
use App\Models\Admin;
use App\Models\Deal;
use App\Models\Employee;
use App\Models\PartnerLocation;
use App\Models\Reel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ApprovalWorkflowTest extends TestCase
{
    use RefreshDatabase;

    protected Employee $employee;

    protected Admin $admin;

    protected Deal $deal;

    protected Reel $reel;

    protected PartnerLocation $partnerLocation;

    protected function setUp(): void
    {
        parent::setUp();

        $this->employee = Employee::factory()->create();
        $this->admin = Admin::factory()->create();
        $this->deal = Deal::factory()->create(['approval_status' => ApprovalStatus::DRAFT]);
        $this->reel = Reel::factory()->create(['approval_status' => ApprovalStatus::DRAFT]);
        $this->partnerLocation = PartnerLocation::factory()->create(['approval_status' => ApprovalStatus::DRAFT]);
    }

    /** @test */
    public function it_can_check_if_record_is_draft()
    {
        $this->assertTrue($this->deal->isDraft());
        $this->assertFalse($this->deal->isPending());
        $this->assertFalse($this->deal->isApproved());
        $this->assertFalse($this->deal->isRejected());
    }

    /** @test */
    public function it_can_submit_for_approval()
    {
        $formData = [
            'title' => 'Updated Deal Title',
            'description' => 'Updated description',
            'max_saving' => 100,
        ];

        $this->deal->submitForApproval($formData, $this->employee->id);

        $this->assertTrue($this->deal->isPending());
        $this->assertEquals($formData, $this->deal->pending_data);
        $this->assertEquals($this->employee->id, $this->deal->submitted_by);
        $this->assertNotNull($this->deal->submitted_at);
    }

    /** @test */
    public function it_can_approve_changes()
    {
        $formData = [
            'title' => 'Updated Deal Title',
            'description' => 'Updated description',
            'max_saving' => 100,
        ];

        $this->deal->submitForApproval($formData, $this->employee->id);
        $this->deal->approve($this->admin->id);
        $this->deal->refresh();

        $this->assertTrue($this->deal->isApproved());
        $this->assertEquals('Updated Deal Title', $this->deal->title);
        $this->assertEquals('Updated description', $this->deal->description);
        $this->assertEquals(100, $this->deal->max_saving);
        $this->assertNull($this->deal->pending_data);
        $this->assertEquals($this->admin->id, $this->deal->reviewed_by);
        $this->assertNotNull($this->deal->reviewed_at);
    }

    /** @test */
    public function it_can_reject_changes()
    {
        $formData = [
            'title' => 'Updated Deal Title',
            'description' => 'Updated description',
        ];
        $rejectionReason = 'Title is not appropriate';

        $this->deal->submitForApproval($formData, $this->employee->id);
        $this->deal->reject($this->admin->id, $rejectionReason);

        $this->assertTrue($this->deal->isRejected());
        $this->assertEquals($rejectionReason, $this->deal->rejection_reason);
        $this->assertEquals($this->admin->id, $this->deal->reviewed_by);
        $this->assertNotNull($this->deal->reviewed_at);

        // Original data should remain unchanged
        $this->assertNotEquals('Updated Deal Title', $this->deal->title);
    }

    /** @test */
    public function it_can_withdraw_submission()
    {
        $formData = [
            'title' => 'Updated Deal Title',
            'description' => 'Updated description',
        ];

        $this->deal->submitForApproval($formData, $this->employee->id);
        $this->deal->withdraw();

        $this->assertTrue($this->deal->isDraft());
        $this->assertNull($this->deal->pending_data);
        $this->assertNull($this->deal->submitted_by);
        $this->assertNull($this->deal->submitted_at);
        $this->assertNull($this->deal->reviewed_by);
        $this->assertNull($this->deal->reviewed_at);
        $this->assertNull($this->deal->rejection_reason);
    }

    /** @test */
    public function it_can_check_if_record_can_be_edited()
    {
        // Draft can be edited
        $this->assertTrue($this->deal->canBeEdited());

        // Pending cannot be edited
        $this->deal->submitForApproval(['title' => 'Test'], $this->employee->id);
        $this->assertFalse($this->deal->canBeEdited());

        // Approved can be edited
        $this->deal->approve($this->admin->id);
        $this->assertTrue($this->deal->canBeEdited());

        // Rejected can be edited
        $this->deal->update(['approval_status' => ApprovalStatus::REJECTED]);
        $this->assertTrue($this->deal->canBeEdited());
    }

    /** @test */
    public function it_can_get_current_data_when_pending()
    {
        $originalTitle = $this->deal->title;
        $formData = [
            'title' => 'Updated Deal Title',
            'description' => 'Updated description',
        ];

        $this->deal->submitForApproval($formData, $this->employee->id);
        $currentData = $this->deal->getCurrentData();

        $this->assertEquals('Updated Deal Title', $currentData['title']);
        $this->assertEquals('Updated description', $currentData['description']);
    }

    /** @test */
    public function it_can_get_original_data()
    {
        $originalTitle = $this->deal->title;
        $formData = [
            'title' => 'Updated Deal Title',
            'description' => 'Updated description',
        ];

        $this->deal->submitForApproval($formData, $this->employee->id);
        $originalData = $this->deal->getOriginalData();

        $this->assertEquals($originalTitle, $originalData['title']);
        $this->assertArrayNotHasKey('pending_data', $originalData);
    }

    /** @test */
    public function it_works_with_reel_model()
    {
        $formData = [
            'caption' => 'Updated Reel Caption',
            'title' => 'Updated Title',
        ];

        $this->reel->submitForApproval($formData, $this->employee->id);
        $this->assertTrue($this->reel->isPending());

        $this->reel->approve($this->admin->id);
        $this->reel->refresh();
        $this->assertTrue($this->reel->isApproved());
        $this->assertEquals('Updated Reel Caption', $this->reel->caption);
    }

    /** @test */
    public function it_works_with_partner_location_model()
    {
        $formData = [
            'name' => 'Updated Location Name',
            'address_line_1' => 'Updated Address',
        ];

        $this->partnerLocation->submitForApproval($formData, $this->employee->id);
        $this->assertTrue($this->partnerLocation->isPending());

        $this->partnerLocation->approve($this->admin->id);
        $this->partnerLocation->refresh();
        $this->assertTrue($this->partnerLocation->isApproved());
        $this->assertEquals('Updated Location Name', $this->partnerLocation->name);
    }

    /** @test */
    public function it_has_relationships_to_employee_and_admin()
    {
        $this->deal->submitForApproval(['title' => 'Test'], $this->employee->id);
        $this->deal->approve($this->admin->id);

        $this->assertEquals($this->employee->id, $this->deal->submittedBy->id);
        $this->assertEquals($this->admin->id, $this->deal->reviewedBy->id);
    }

    /** @test */
    public function it_can_scope_by_approval_status()
    {
        $draftDeal = Deal::factory()->create(['approval_status' => ApprovalStatus::DRAFT]);
        $pendingDeal = Deal::factory()->create(['approval_status' => ApprovalStatus::PENDING]);
        $approvedDeal = Deal::factory()->create(['approval_status' => ApprovalStatus::APPROVED]);
        $rejectedDeal = Deal::factory()->create(['approval_status' => ApprovalStatus::REJECTED]);

        $this->assertEquals(2, Deal::draft()->count()); // including the one from setUp
        $this->assertEquals(1, Deal::pending()->count());
        $this->assertEquals(1, Deal::approved()->count());
        $this->assertEquals(1, Deal::rejected()->count());
    }
}
