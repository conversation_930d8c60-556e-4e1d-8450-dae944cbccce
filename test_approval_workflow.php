<?php

require_once 'vendor/autoload.php';

use App\Enums\ApprovalStatus;
use App\Models\Deal;
use App\Models\Employee;
use App\Models\Admin;

// Test the approval workflow
echo "Testing Approval Workflow...\n";

// Create test data
$employee = Employee::factory()->create(['name' => 'Test Employee']);
$admin = Admin::factory()->create(['name' => 'Test Admin']);

// Create a deal
$deal = Deal::factory()->create([
    'title' => 'Original Deal Title',
    'description' => 'Original Description',
    'max_saving' => 50,
    'approval_status' => ApprovalStatus::DRAFT,
]);

echo "1. Created deal with title: '{$deal->title}'\n";

// Test submitting for approval
$pendingData = [
    'title' => 'Updated Deal Title',
    'description' => 'Updated Description',
    'max_saving' => 100,
];

$deal->submitForApproval($pendingData, $employee->id);
echo "2. Submitted for approval with new title: '{$pendingData['title']}'\n";
echo "   Status: {$deal->approval_status->value}\n";
echo "   Pending data: " . json_encode($deal->pending_data) . "\n";

// Test getting changes for admin view
$originalData = $deal->getOriginalData();
$changes = [];

foreach ($deal->pending_data as $key => $newValue) {
    $oldValue = $originalData[$key] ?? null;
    if ($oldValue != $newValue) {
        $changes[] = [
            'field' => ucfirst(str_replace('_', ' ', $key)),
            'old' => $oldValue,
            'new' => $newValue,
        ];
    }
}

echo "3. Changes detected:\n";
foreach ($changes as $change) {
    echo "   - {$change['field']}: '{$change['old']}' → '{$change['new']}'\n";
}

// Test approval
$deal->approve($admin->id);
$deal->refresh();

echo "4. After approval:\n";
echo "   Title: '{$deal->title}'\n";
echo "   Description: '{$deal->description}'\n";
echo "   Max saving: {$deal->max_saving}\n";
echo "   Status: {$deal->approval_status->value}\n";
echo "   Pending data: " . ($deal->pending_data ? json_encode($deal->pending_data) : 'null') . "\n";

echo "\nTest completed successfully!\n";
