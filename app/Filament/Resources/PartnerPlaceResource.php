<?php

namespace App\Filament\Resources;

use App\Enums\TagType;
use App\Filament\Resources\PartnerPlaceResource\Pages;
use App\Filament\Resources\PartnerPlaceResource\RelationManagers\DealsRelationManager;
use App\Filament\Resources\PartnerPlaceResource\RelationManagers\ReelsRelationManager;
use App\Forms\Components\PartnerPlaceTagsInput;
use App\Models\PartnerLocation;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Resource;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;

class PartnerPlaceResource extends Resource
{
    protected static ?string $model = PartnerLocation::class;

    protected static ?string $slug = 'partner-places';

    protected static ?string $modelLabel = 'Partner Place';

    protected static ?string $navigationIcon = 'heroicon-o-bookmark-square';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('partner_id')
                    ->relationship('partner', 'name')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->hidden(fn ($livewire) => $livewire instanceof RelationManager),

                TextInput::make('name')
                    ->required()
                    ->maxLength(20)
                    ->helperText('Maximum 20 characters'),

                PhoneInput::make('phone')
                    ->label('Phone Number')
                    ->required()
                    ->displayNumberFormat(PhoneInputNumberType::INTERNATIONAL)
                    ->inputNumberFormat(PhoneInputNumberType::INTERNATIONAL)
                    ->defaultCountry('ae')
                    ->initialCountry('ae')
                    ->columnSpanFull(),

                Section::make('Tags')
                    ->statePath('')
                    ->collapsible()
                    ->schema([
                        TextInput::make('price_per_person')
                            ->inputMode('decimal'),

                        PartnerPlaceTagsInput::make('Meal Times')
                            ->type(TagType::MEAL_TIMES->value),

                        PartnerPlaceTagsInput::make('Specialities')
                            ->type(TagType::SPECIALITIES->value),

                        PartnerPlaceTagsInput::make('Service Options')
                            ->type(TagType::SERVICE_OPTIONS->value),

                        PartnerPlaceTagsInput::make('Dietary')
                            ->type(TagType::DIETARY->value),

                        PartnerPlaceTagsInput::make('Parking')
                            ->type(TagType::PARKING->value),

                        PartnerPlaceTagsInput::make(TagType::CRAVINGS->getLabel())
                            ->type(TagType::CRAVINGS->value),

                        Grid::make(2)
                            ->schema([
                                PartnerPlaceTagsInput::make('Cuisine Types')
                                    ->type(TagType::CUISINE_TYPES->value),

                                PartnerPlaceTagsInput::make('ambiance')
                                    ->type(TagType::AMBIANCE->value),
                            ])
                            ->columnSpanFull(),

                        PartnerPlaceTagsInput::make('Areas')
                            ->maxItems(1)
                            ->type(TagType::AREA->value),

                        PartnerPlaceTagsInput::make('Retail Destinations')
                            ->maxItems(1)
                            ->type(TagType::RETAIL_DESTINATION->value),

                    ]),

                TextInput::make('address_line_1')
                    ->label('Address Line 1')
                    ->columnSpanFull()
                    ->placeholder('Enter street address'),

                TextInput::make('address_line_2')
                    ->label('Address Line 2')
                    ->columnSpanFull()
                    ->placeholder('Apartment, suite, etc. (optional)'),

                Grid::make(2)->schema([
                    TextInput::make('city')
                        ->label('City'),

                    TextInput::make('state')
                        ->label('State'),
                ]),

                Grid::make(2)->schema([
                    TextInput::make('postal_code')
                        ->label('Postal Code'),

                    TextInput::make('country')
                        ->label('Country'),
                ]),

                Grid::make(2)
                    ->schema([
                        TextInput::make('lat')
                            ->label('Latitude')
                            ->numeric()
                            ->minValue(-90)
                            ->maxValue(90)
                            ->required()
                            ->placeholder('e.g. 37.7749'),

                        TextInput::make('lng')
                            ->label('Longitude')
                            ->numeric()
                            ->minValue(-180)
                            ->maxValue(180)
                            ->required()
                            ->placeholder('e.g. -122.4194'),
                    ]),

                Fieldset::make('Rates')
                    ->columns()
                    ->schema([
                        TextInput::make('google_rate')
                            ->numeric()
                            ->inputMode('decimal')
                            ->required()
                            ->minValue(0)
                            ->maxValue(5),

                        TextInput::make('reviews_count')
                            ->numeric()
                            ->integer()
                            ->required(),
                    ]),

                Section::make([
                    TableRepeater::make('opening_hours')
                        ->label('Opening Hours')
                        ->default(self::getDefaultOpeningHours())
                        ->required()
                        ->minItems(1)
                        ->headers([
                            Header::make('Day'),
                            Header::make('Open'),
                            Header::make('Close'),
                        ])
                        ->schema([
                            Select::make('day')
                                ->label('Day')
                                ->options(fn () => Carbon::getDays())
                                ->searchable()
                                ->preload()
                                ->required()
                                ->disableOptionsWhenSelectedInSiblingRepeaterItems(),

                            TimePicker::make('from')
                                ->required()
                                ->seconds(false)
                                ->label('Open'),

                            TimePicker::make('to')
                                ->required()
                                ->seconds(false)
                                ->label('Close'),
                        ]),
                ]),

                SpatieMediaLibraryFileUpload::make('avatar')
                    ->label('Avatar')
                    ->collection('avatar')
                    ->required()
                    ->image()
                    ->getUploadedFileNameForStorageUsing(function (TemporaryUploadedFile $file): string {
                        return Str::random(32).'.'.$file->getClientOriginalExtension();
                    })
                    ->visibility('public'),

                SpatieMediaLibraryFileUpload::make('images')
                    ->label('Images')
                    ->collection('images')
                    ->visibility('public')
                    ->multiple()
                    ->required()
                    ->minFiles(1)
                    ->image()
                    ->getUploadedFileNameForStorageUsing(function (TemporaryUploadedFile $file): string {
                        return Str::random(32).'.'.$file->getClientOriginalExtension();
                    })
                    ->columnSpanFull(),

                SpatieMediaLibraryFileUpload::make('menu')
                    ->label('Menu')
                    ->collection('menu')
                    ->visibility('public')
                    ->multiple()
                    ->getUploadedFileNameForStorageUsing(function (TemporaryUploadedFile $file): string {
                        return Str::random(32).'.'.$file->getClientOriginalExtension();
                    })
                    ->acceptedFileTypes([
                        'application/pdf',
                        'image/*',
                    ])
                    ->columnSpanFull(),

                TextInput::make('menu_url')
                    ->url(),
            ])
            ->disabled(fn (?PartnerLocation $record): bool => $record?->trashed() ?? false);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->color(fn ($record) => $record->trashed() ? 'gray' : null),

                TextColumn::make('partner.name')
                    ->searchable()
                    ->color(fn ($record) => $record->trashed() ? 'gray' : null),

                SpatieMediaLibraryImageColumn::make('avatar')
                    ->collection('avatar'),

                TextColumn::make('address_line_1')
                    ->searchable()
                    ->color(fn ($record) => $record->trashed() ? 'gray' : null)
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->actions([
                ActionGroup::make([
                    PartnerLocation::getReplicateAction(),

                    ActionGroup::make([
                        EditAction::make()->hidden(fn ($record) => $record->trashed()),
                        DeleteAction::make()->hidden(fn ($record) => $record->trashed()),
                        ForceDeleteAction::make()->visible(fn ($record) => $record->trashed()),
                        RestoreAction::make()->visible(fn ($record) => $record->trashed()),
                    ])->dropdown(false),
                ]),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ])
            ->recordUrl(
                fn (?PartnerLocation $record): ?string => $record?->trashed() ? null : Pages\EditPartnerPlace::getUrl(['record' => $record])
            );
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPartnerPlaces::route('/'),
            'create' => Pages\CreatePartnerPlace::route('/create'),
            'edit' => Pages\EditPartnerPlace::route('/{record}/edit'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            DealsRelationManager::class,
            ReelsRelationManager::class,
        ];
    }

    private static function getDefaultOpeningHours(): array
    {
        return collect(Carbon::getDays())->map(function ($day, $key) {
            return [
                'day' => $key,
                'from' => '09:00',
                'to' => '21:00',
            ];
        })->toArray();
    }
}
