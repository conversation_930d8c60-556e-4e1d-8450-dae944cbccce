<?php

use App\Enums\InvitationStatus;
use App\Filament\Resources\InvitationResource\Pages\ListInvitations;
use App\Models\Admin;
use App\Models\Invitation;
use App\Models\User;
use App\Notifications\InvitationApprovedNotification;
use Illuminate\Support\Facades\Notification;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('admin can approve invitation and notification is sent to user with fcm token', function () {
    Notification::fake();

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @var User $user */
    $user = User::factory()->create([
        'email' => '<EMAIL>',
        'fcm_token' => 'test_fcm_token_123',
    ]);

    /** @var Invitation $invitation */
    $invitation = Invitation::factory()->create([
        'email' => '<EMAIL>',
        'status' => InvitationStatus::PENDING,
    ]);

    livewire(ListInvitations::class)
        ->callTableAction('approve', $invitation->id);

    // Check invitation status was updated
    expect($invitation->fresh()->status)->toBe(InvitationStatus::APPROVED);

    // Check notification was sent
    Notification::assertSentTo(
        $user,
        InvitationApprovedNotification::class
    );
});

test('admin can approve invitation but notification is not sent to user without fcm token', function () {
    Notification::fake();

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @var User $user */
    $user = User::factory()->create([
        'email' => '<EMAIL>',
        'fcm_token' => null, // No FCM token
    ]);

    /** @var Invitation $invitation */
    $invitation = Invitation::factory()->create([
        'email' => '<EMAIL>',
        'status' => InvitationStatus::PENDING,
    ]);

    livewire(ListInvitations::class)
        ->callTableAction('approve', $invitation->id);

    // Check invitation status was updated
    expect($invitation->fresh()->status)->toBe(InvitationStatus::APPROVED);

    // Check notification was NOT sent
    Notification::assertNotSentTo(
        $user,
        InvitationApprovedNotification::class
    );
});

test('admin can approve invitation but notification is not sent when user does not exist', function () {
    Notification::fake();

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @var Invitation $invitation */
    $invitation = Invitation::factory()->create([
        'email' => '<EMAIL>', // User doesn't exist
        'status' => InvitationStatus::PENDING,
    ]);

    livewire(ListInvitations::class)
        ->callTableAction('approve', $invitation->id);

    // Check invitation status was updated
    expect($invitation->fresh()->status)->toBe(InvitationStatus::APPROVED);

    // Check no notifications were sent
    Notification::assertNothingSent();
});

test('approve action is not available for already approved invitations', function () {
    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @var Invitation $invitation */
    $invitation = Invitation::factory()->create([
        'status' => InvitationStatus::APPROVED,
    ]);

    // The approve action should be hidden for already approved invitations
    // This test verifies that the action is properly hidden
    livewire(ListInvitations::class)
        ->assertTableActionHidden('approve', $invitation);
});

test('approve action works correctly for pending invitations', function () {
    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @var Invitation $invitation */
    $invitation = Invitation::factory()->create([
        'status' => InvitationStatus::PENDING,
    ]);

    // This test verifies that the approve action exists and works
    livewire(ListInvitations::class)
        ->callTableAction('approve', $invitation->id);

    // Status should be updated to approved
    expect($invitation->fresh()->status)->toBe(InvitationStatus::APPROVED);
});

test('only admin users can access approve action', function () {
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'web');

    /** @var Invitation $invitation */
    $invitation = Invitation::factory()->create([
        'status' => InvitationStatus::PENDING,
    ]);

    livewire(ListInvitations::class)
        ->assertForbidden();
});
