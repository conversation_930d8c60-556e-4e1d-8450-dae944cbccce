<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes; // Add this line

class Collection extends Model
{
    use HasFactory, SoftDeletes; // Add SoftDeletes here

    protected $guarded = ['id'];

    protected static function booted(): void
    {
        static::deleting(function (Collection $collection) {
            // Delete all CollectionItem relationships
            CollectionItem::where('collection_id', $collection->id)->delete();
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(CollectionItem::class);
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'collection_user')
            ->withPivot('role')
            ->withTimestamps();
    }

    public function myRole(): string
    {
        $user = auth()->user();

        if (! $user) {
            return '';
        }
        $pivot = $this->users()->where('user_id', $user->id)->first()?->pivot;

        return $pivot->role ?? '';
    }
}
