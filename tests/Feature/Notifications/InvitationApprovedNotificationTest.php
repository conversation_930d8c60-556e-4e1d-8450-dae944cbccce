<?php

use App\Models\User;
use App\Notifications\InvitationApprovedNotification;
use Illuminate\Support\Facades\Notification;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('notification is sent when invitation is approved', function () {
    Notification::fake();

    /** @var User $user */
    $user = User::factory()->create([
        'fcm_token' => 'test_fcm_token_123',
    ]);

    $user->notify(new InvitationApprovedNotification);

    Notification::assertSentTo(
        $user,
        InvitationApprovedNotification::class,
        function ($notification, $channels) {
            $data = $notification->toArray($notification);

            return $data['type'] === 'invitation_approved'
                && $data['title'] === 'You\'re In! 🎉'
                && $data['message'] === 'Welcome to Conari. Explore curated dining spots now.'
                && $data['deep_link'] === 'conari://home'
                && $data['overlay_message'] === 'Welcome to Conari 👋 You\'re now an Insider. Start exploring.'
                && $data['show_overlay'] === true;
        }
    );
});

test('notification uses correct channels', function () {
    /** @var User $user */
    $user = User::factory()->create([
        'fcm_token' => 'test_fcm_token_123',
    ]);

    $notification = new InvitationApprovedNotification;
    $channels = $notification->via($user);

    expect($channels)->toContain('database');
    expect($channels)->toContain(\NotificationChannels\Fcm\FcmChannel::class);
});

test('fcm notification has correct structure and content', function () {
    /** @var User $user */
    $user = User::factory()->create([
        'fcm_token' => 'test_fcm_token_123',
    ]);

    $notification = new InvitationApprovedNotification;
    $fcmMessage = $notification->toFcm($user);

    // Check data payload
    expect($fcmMessage->data)
        ->toHaveKey('type', 'invitation_approved')
        ->toHaveKey('user_id', (string) $user->id)
        ->toHaveKey('deep_link', 'conari://home')
        ->toHaveKey('overlay_message', 'Welcome to Conari 👋 You\'re now an Insider. Start exploring.')
        ->toHaveKey('show_overlay', 'true');
});

test('database notification has correct structure', function () {
    /** @var User $user */
    $user = User::factory()->create([
        'fcm_token' => 'test_fcm_token_123',
    ]);

    $notification = new InvitationApprovedNotification;
    $databaseData = $notification->toArray($user);

    expect($databaseData)
        ->toHaveKey('type', 'invitation_approved')
        ->toHaveKey('title', 'You\'re In! 🎉')
        ->toHaveKey('message', 'Welcome to Conari. Explore curated dining spots now.')
        ->toHaveKey('user_id', $user->id)
        ->toHaveKey('deep_link', 'conari://home')
        ->toHaveKey('overlay_message', 'Welcome to Conari 👋 You\'re now an Insider. Start exploring.')
        ->toHaveKey('show_overlay', true);
});

test('notification implements ShouldQueue interface', function () {
    $notification = new InvitationApprovedNotification;

    expect($notification)->toBeInstanceOf(\Illuminate\Contracts\Queue\ShouldQueue::class);
});

test('notification can be instantiated without parameters', function () {
    $notification = new InvitationApprovedNotification;

    expect($notification)->toBeInstanceOf(InvitationApprovedNotification::class);
    expect($notification)->toBeInstanceOf(\Illuminate\Notifications\Notification::class);
});

test('notification works with user without fcm token', function () {
    /** @var User $user */
    $user = User::factory()->create([
        'fcm_token' => null,
    ]);

    $notification = new InvitationApprovedNotification;

    // Should still return channels even if user has no FCM token
    $channels = $notification->via($user);
    expect($channels)->toContain('database');
    expect($channels)->toContain(\NotificationChannels\Fcm\FcmChannel::class);

    // Database notification should still work
    $databaseData = $notification->toArray($user);
    expect($databaseData)->toHaveKey('type', 'invitation_approved');
});

test('fcm notification data contains all required fields', function () {
    /** @var User $user */
    $user = User::factory()->create([
        'fcm_token' => 'test_fcm_token_123',
    ]);

    $notification = new InvitationApprovedNotification;
    $fcmMessage = $notification->toFcm($user);

    // Verify all data fields are present and correct type
    expect($fcmMessage->data)->toHaveKeys([
        'type',
        'user_id',
        'deep_link',
        'overlay_message',
        'show_overlay',
    ]);

    expect($fcmMessage->data['type'])->toBe('invitation_approved');
    expect($fcmMessage->data['user_id'])->toBe((string) $user->id);
    expect($fcmMessage->data['deep_link'])->toBe('conari://home');
    expect($fcmMessage->data['show_overlay'])->toBe('true');
});

test('fcm notification has correct notification object structure', function () {
    /** @var User $user */
    $user = User::factory()->create([
        'fcm_token' => 'test_fcm_token_123',
    ]);

    $notification = new InvitationApprovedNotification;
    $fcmMessage = $notification->toFcm($user);

    expect($fcmMessage->notification)->toBeInstanceOf(\NotificationChannels\Fcm\Resources\Notification::class);
    expect($fcmMessage->notification->title)->toBe('You\'re In! 🎉');
    expect($fcmMessage->notification->body)->toBe('Welcome to Conari. Explore curated dining spots now.');
});

test('android fcm configuration is complete', function () {
    /** @var User $user */
    $user = User::factory()->create([
        'fcm_token' => 'test_fcm_token_123',
    ]);

    $notification = new InvitationApprovedNotification;
    $fcmMessage = $notification->toFcm($user);

    $androidConfig = $fcmMessage->custom['android'];

    expect($androidConfig)->toHaveKey('notification');
    expect($androidConfig)->toHaveKey('fcm_options');

    expect($androidConfig['notification'])->toHaveKeys([
        'click_action',
        'color',
        'sound',
        'icon',
        'channel_id',
    ]);

    expect($androidConfig['fcm_options'])->toHaveKey('analytics_label');
});

test('ios fcm configuration is complete', function () {
    /** @var User $user */
    $user = User::factory()->create([
        'fcm_token' => 'test_fcm_token_123',
    ]);

    $notification = new InvitationApprovedNotification;
    $fcmMessage = $notification->toFcm($user);

    $iosConfig = $fcmMessage->custom['apns'];

    expect($iosConfig)->toHaveKey('payload');
    expect($iosConfig)->toHaveKey('fcm_options');

    expect($iosConfig['payload'])->toHaveKey('aps');
    expect($iosConfig['payload']['aps'])->toHaveKeys(['category', 'sound']);
    expect($iosConfig['fcm_options'])->toHaveKey('analytics_label');
});

test('notification can be serialized and unserialized', function () {
    $notification = new InvitationApprovedNotification;

    $serialized = serialize($notification);
    $unserialized = unserialize($serialized);

    expect($unserialized)->toBeInstanceOf(InvitationApprovedNotification::class);
    expect($unserialized)->toBeInstanceOf(\Illuminate\Contracts\Queue\ShouldQueue::class);
});

test('notification works with different user IDs', function () {
    $testCases = [
        ['fcm_token' => 'test_fcm_token_1'],
        ['fcm_token' => 'test_fcm_token_2'],
        ['fcm_token' => 'test_fcm_token_3'],
    ];

    foreach ($testCases as $userData) {
        /** @var User $user */
        $user = User::factory()->create($userData);

        $notification = new InvitationApprovedNotification;
        $fcmMessage = $notification->toFcm($user);
        $databaseData = $notification->toArray($user);

        expect($fcmMessage->data['user_id'])->toBe((string) $user->id);
        expect($databaseData['user_id'])->toBe($user->id);
    }
});

test('notification can be sent to multiple users simultaneously', function () {
    Notification::fake();

    $users = User::factory()->count(3)->create([
        'fcm_token' => 'test_fcm_token_multi',
    ]);

    $notification = new InvitationApprovedNotification;

    // Send to all users
    Notification::send($users, $notification);

    // Verify each user received the notification
    foreach ($users as $user) {
        Notification::assertSentTo($user, InvitationApprovedNotification::class);
    }
});

test('notification maintains consistency across channels', function () {
    /** @var User $user */
    $user = User::factory()->create([
        'fcm_token' => 'test_fcm_token_123',
    ]);

    $notification = new InvitationApprovedNotification;
    $fcmMessage = $notification->toFcm($user);
    $databaseData = $notification->toArray($user);

    // Title should be consistent
    expect($fcmMessage->notification->title)->toBe($databaseData['title']);

    // Type should be consistent
    expect($fcmMessage->data['type'])->toBe($databaseData['type']);

    // User ID should be consistent
    expect($fcmMessage->data['user_id'])->toBe((string) $databaseData['user_id']);

    // Deep link should be consistent
    expect($fcmMessage->data['deep_link'])->toBe($databaseData['deep_link']);
});
