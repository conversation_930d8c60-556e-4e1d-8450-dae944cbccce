<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum TagType: string implements <PERSON><PERSON>abe<PERSON>
{
    case MEAL_TIMES = 'meal_times';
    case DIETARY = 'dietary';
    case AMBIANCE = 'ambiance';
    case SPECIALITIES = 'specialities';
    case CUISINE_TYPES = 'cuisine_types';
    case SERVICE_OPTIONS = 'service_options';
    case PARKING = 'parking';
    case CRAVINGS = 'cravings';
    case AREA = 'area';
    case RETAIL_DESTINATION = 'retail_destination';

    public function getLabel(): string
    {
        return match ($this) {
            self::DIETARY => 'Dietary',
            self::MEAL_TIMES => 'Meal Times',
            self::AMBIANCE => 'Ambiance',
            self::SPECIALITIES => 'Specialities',
            self::CUISINE_TYPES => 'Cuisine Types',
            self::SERVICE_OPTIONS => 'Service Options',
            self::PARKING => 'Parking',
            self::CRAVINGS => 'Cravings',
            self::AREA => 'Area',
            self::RET<PERSON>IL_DESTINATION => 'Retail Destination',
        };
    }
}
