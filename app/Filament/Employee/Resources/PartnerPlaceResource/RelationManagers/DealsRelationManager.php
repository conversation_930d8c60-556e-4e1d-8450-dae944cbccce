<?php

namespace App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers;

use App\Enums\ApprovalStatus;
use App\Filament\Resources\DealResource;
use App\Models\Deal;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class DealsRelationManager extends RelationManager
{
    protected static string $relationship = 'deals';

    public function form(Form $form): Form
    {
        return DealResource::form($form)->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->sortable(),
                TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('deal_type')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('approval_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (ApprovalStatus $state): string => match ($state) {
                        ApprovalStatus::DRAFT => 'gray',
                        ApprovalStatus::PENDING => 'warning',
                        ApprovalStatus::APPROVED => 'success',
                        ApprovalStatus::REJECTED => 'danger',
                    })
                    ->icon(fn (ApprovalStatus $state): string => match ($state) {
                        ApprovalStatus::DRAFT => 'heroicon-o-document',
                        ApprovalStatus::PENDING => 'heroicon-o-clock',
                        ApprovalStatus::APPROVED => 'heroicon-o-check-circle',
                        ApprovalStatus::REJECTED => 'heroicon-o-x-circle',
                    }),
                TextColumn::make('submitted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('reviewed_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->headerActions([
                CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        // Set the partner location ID and initial status
                        $data['partner_location_id'] = $this->getOwnerRecord()->getKey();
                        $data['approval_status'] = ApprovalStatus::DRAFT;
                        return $data;
                    })
                    ->successNotification(
                        Notification::make()
                            ->success()
                            ->title('Deal created')
                            ->body('The deal has been created as a draft. Submit it for approval when ready.')
                    ),
            ])
            ->actions([
                EditAction::make()
                    ->url(fn (Deal $record): string => \App\Filament\Employee\Resources\DealResource::getUrl('edit', ['record' => $record->getKey()]))
                    ->visible(fn (Deal $record): bool => $record->canBeEdited()),
                Action::make('submit_for_approval')
                    ->label('Submit for Approval')
                    ->icon('heroicon-o-paper-airplane')
                    ->color('success')
                    ->visible(fn (Deal $record): bool => $record->isDraft() || $record->isRejected())
                    ->action(function (Deal $record): void {
                        $record->submitForApproval($record->getAttributes(), auth('employee')->id());
                        Notification::make()
                            ->title('Deal submitted for approval')
                            ->success()
                            ->send();
                    }),
                Action::make('withdraw')
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->color('warning')
                    ->visible(fn (Deal $record): bool => $record->isPending())
                    ->action(function (Deal $record): void {
                        $record->withdraw();
                        Notification::make()
                            ->title('Deal submission withdrawn')
                            ->success()
                            ->send();
                    }),
                DeleteAction::make()
                    ->visible(fn (Deal $record): bool => $record->canBeEdited()),
            ]);
    }
}
