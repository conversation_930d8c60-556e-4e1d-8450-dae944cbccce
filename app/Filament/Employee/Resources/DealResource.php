<?php

namespace App\Filament\Employee\Resources;

use App\Enums\ApprovalStatus;
use App\Filament\Employee\Resources\DealResource\Pages;
use App\Models\Deal;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class DealResource extends Resource
{
    protected static ?string $model = Deal::class;

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return \App\Filament\Resources\DealResource::form($form)
            ->schema([
                \Filament\Forms\Components\Select::make('partner_location_id')
                    ->label('Partner Location')
                    ->relationship(
                        'partner_place',
                        'name',
                        fn (\Illuminate\Database\Eloquent\Builder $query) => $query->whereHas('employees',
                            fn (\Illuminate\Database\Eloquent\Builder $q) => $q->where('employee_id', auth('employee')->id())
                        )
                    )
                    ->required()
                    ->searchable()
                    ->preload(),
                ...\App\Filament\Resources\DealResource::form($form)->getComponents(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return \App\Filament\Resources\DealResource::table($table)
            ->columns([
                TextColumn::make('id')
                    ->sortable(),
                TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('deal_type')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('service_type.title')
                    ->label('Service Type')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('approval_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (ApprovalStatus $state): string => match ($state) {
                        ApprovalStatus::DRAFT => 'gray',
                        ApprovalStatus::PENDING => 'warning',
                        ApprovalStatus::APPROVED => 'success',
                        ApprovalStatus::REJECTED => 'danger',
                    })
                    ->icon(fn (ApprovalStatus $state): string => match ($state) {
                        ApprovalStatus::DRAFT => 'heroicon-o-document',
                        ApprovalStatus::PENDING => 'heroicon-o-clock',
                        ApprovalStatus::APPROVED => 'heroicon-o-check-circle',
                        ApprovalStatus::REJECTED => 'heroicon-o-x-circle',
                    }),
                TextColumn::make('submitted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('reviewed_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->actions([
                EditAction::make()
                    ->url(fn (Deal $record): string => DealResource::getUrl('edit', ['record' => $record->getKey()]))
                    ->visible(fn (Deal $record): bool => $record->canBeEdited()),
                Action::make('withdraw')
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->color('warning')
                    ->visible(fn (Deal $record): bool => $record->pendingDraft !== null)
                    ->action(function (Deal $record): void {
                        if ($record->pendingDraft) {
                            $record->pendingDraft->withdraw();
                            Notification::make()
                                ->title('Deal submission withdrawn')
                                ->success()
                                ->send();
                        }
                    }),
                DeleteAction::make()
                    ->visible(fn (Deal $record): bool => $record->canBeEdited()),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeals::route('/'),
            'create' => Pages\CreateDeal::route('/create'),
            'edit' => Pages\EditDeal::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereHas('partner_place.employees', fn (Builder $query) => $query->where('employee_id', auth('employee')->id()));
    }
}
