<?php

namespace App\Filament\Resources;

use App\Enums\ApprovalStatus;
use App\Enums\DealType;
use App\Enums\TagType;
use App\Filament\Resources\DealResource\Pages;
use App\Filament\Resources\DealResource\Pages\CreateDeal;
use App\Filament\Resources\DealResource\RelationManagers\DealSlotsRelationManager;
use App\Filament\Resources\PartnerPlaceResource\Pages\EditPartnerPlace;
use App\Filament\Resources\PartnerPlaceResource\RelationManagers\DealsRelationManager;
use App\Models\Deal;
use App\Models\Tag;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action as TableAction;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\ForceDeleteBulkAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Actions\RestoreBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class DealResource extends Resource
{
    protected static ?string $model = Deal::class;

    protected static ?string $slug = 'deals';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('deal_type')
                    ->required()
                    ->searchable()
                    ->options(DealType::class),

                Select::make('service_type_id')
                    ->label('Service Type')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->relationship('service_type', 'title', fn ($query) => $query->where('type', TagType::SERVICE_OPTIONS->value))
                    ->createOptionForm([
                        TextInput::make('title')
                            ->required()
                            ->maxLength(255),
                        Hidden::make('type')
                            ->default(TagType::SERVICE_OPTIONS->value),
                    ])
                    ->createOptionUsing(function (array $data): int {
                        $tag = Tag::create($data);
                        return $tag->getKey();
                    }),

                TextInput::make('title')
                    ->required()
                    ->maxLength(30)
                    ->helperText('Maximum 30 characters'),

                TextInput::make('description')
                    ->required(),

                TextInput::make('max_saving')
                    ->prefix('AED')
                    ->default(0)
                    ->required()
                    ->numeric(),

                Hidden::make('valid_days')
                    ->default(array_keys(Carbon::getDays())),

                Section::make('Slots')
                    ->statePath('slots')
                    ->columns(2)
                    ->schema(fn (Get $get, Set $set): array => [
                        TableRepeater::make('slot')
                            ->label('')
                            ->relationship('dealSlots')
                            ->columns(2)
                            ->minItems(1)
                            ->headers([
                                Header::make('day'),
                                Header::make('from'),
                                Header::make('to'),
                            ])
                            ->columnSpanFull()
                            ->extraItemActions([
                                Action::make('copy')
                                    ->icon('heroicon-o-document-duplicate')
                                    ->action(fn () => now())
                                    ->form([
                                        CheckboxList::make('days')
                                            ->label('Day')
                                            ->options(Carbon::getDays()),
                                    ])
                                    ->action(function (array $arguments, Repeater $component, array $data) use ($set, $get) {
                                        $oldSlots = $get('../*.slots.slot');

                                        $copiedSlot = $component->getRawItemState($arguments['item']);

                                        $copiedSlots = [];

                                        foreach ($data['days'] as $day) {
                                            $copiedSlot['day'] = $day;
                                            $copiedSlots[] = $copiedSlot;
                                        }

                                        $set('../*.slots.slot', array_values(array_merge($oldSlots[0], $copiedSlots)));
                                    }),
                            ])
                            ->schema([
                                Select::make('day')
                                    ->label('Day')
                                    ->options(Carbon::getDays())
                                    ->searchable()
                                    ->preload(),

                                TimePicker::make('from')
                                    ->seconds(false)
                                    ->label('From'),

                                TimePicker::make('to')
                                    ->seconds(false)
                                    ->label('To'),
                            ]),
                    ]),

                TextInput::make('max_usage_per_day')
                    ->label('Maximum Booking Per Day(Slots)')
                    ->numeric()
                    ->required()
                    ->default(1),

                TextInput::make('reuse_limit_days')
                    ->label('Deal Reuse Limit')
                    ->hint('Set how often customers can reuse this deal')
                    ->placeholder('Days between uses')
                    ->required()
                    ->numeric(),
            ])->disabled(fn (?Deal $record): bool => $record?->trashed() ?? false); // Changed Model to Deal
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->sortable()
                    ->color(fn (?Deal $record) => $record?->trashed() ? 'gray' : null),
                TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->color(fn (?Deal $record) => $record?->trashed() ? 'gray' : null),
                TextColumn::make('deal_type')
                    ->searchable()
                    ->sortable()
                    ->color(fn (?Deal $record) => $record?->trashed() ? 'gray' : null),
                TextColumn::make('service_type.title')
                    ->label('Service Type')
                    ->searchable()
                    ->sortable()
                    ->color(fn (?Deal $record) => $record?->trashed() ? 'gray' : null),
                TextColumn::make('approval_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (ApprovalStatus $state): string => match ($state) {
                        ApprovalStatus::DRAFT => 'gray',
                        ApprovalStatus::PENDING => 'warning',
                        ApprovalStatus::APPROVED => 'success',
                        ApprovalStatus::REJECTED => 'danger',
                    })
                    ->icon(fn (ApprovalStatus $state): string => match ($state) {
                        ApprovalStatus::DRAFT => 'heroicon-o-document',
                        ApprovalStatus::PENDING => 'heroicon-o-clock',
                        ApprovalStatus::APPROVED => 'heroicon-o-check-circle',
                        ApprovalStatus::REJECTED => 'heroicon-o-x-circle',
                    }),
                TextColumn::make('submittedBy.name')
                    ->label('Submitted By')
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('submitted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('reviewedBy.name')
                    ->label('Reviewed By')
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('reviewed_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
                SelectFilter::make('approval_status')
                    ->label('Approval Status')
                    ->options([
                        ApprovalStatus::DRAFT->value => 'Draft',
                        ApprovalStatus::PENDING->value => 'Pending',
                        ApprovalStatus::APPROVED->value => 'Approved',
                        ApprovalStatus::REJECTED->value => 'Rejected',
                    ]),
            ])
            ->actions([
                TableAction::make('view_changes')
                    ->label('View Changes')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->visible(fn (Deal $record): bool => $record->isPending())
                    ->modalHeading('Pending Changes')
                    ->modalContent(function (Deal $record) {
                        $originalData = $record->getOriginalData();
                        $pendingData = $record->pending_data ?? [];

                        $changes = [];

                        // Debug: Log the data for troubleshooting
                        \Log::info('View Changes Debug', [
                            'record_id' => $record->id,
                            'original_data' => $originalData,
                            'pending_data' => $pendingData,
                            'original_service_type_id' => $originalData['service_type_id'] ?? 'not set',
                            'pending_service_type_id' => $pendingData['service_type_id'] ?? 'not set',
                        ]);

                        // If we have pending data, compare it with original
                        if (!empty($pendingData)) {
                            foreach ($pendingData as $key => $newValue) {
                                // Skip approval-related fields and timestamps
                                if (in_array($key, ['approval_status', 'pending_data', 'submitted_by', 'submitted_at', 'reviewed_by', 'reviewed_at', 'rejection_reason', 'created_at', 'updated_at', 'deleted_at', 'id'])) {
                                    continue;
                                }

                                $oldValue = $originalData[$key] ?? null;

                                // Handle type conversion for proper comparison
                                $oldValueForComparison = $oldValue;
                                $newValueForComparison = $newValue;

                                // Convert numeric strings to integers for comparison
                                if (is_numeric($oldValue) && is_numeric($newValue)) {
                                    $oldValueForComparison = (int) $oldValue;
                                    $newValueForComparison = (int) $newValue;
                                }

                                // Only show if there's actually a change
                                if ($oldValueForComparison != $newValueForComparison) {
                                    $changes[] = [
                                        'field' => ucfirst(str_replace('_', ' ', $key)),
                                        'old' => static::formatValue($oldValue, $key, $record),
                                        'new' => static::formatValue($newValue, $key, $record),
                                    ];
                                }
                            }
                        }

                        return view('filament.admin.deal-changes', [
                            'changes' => $changes,
                            'submittedBy' => $record->submittedBy?->name,
                            'submittedAt' => $record->submitted_at?->format('M j, Y g:i A'),
                        ]);
                    }),
                TableAction::make('approve')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn (Deal $record): bool => $record->isPending())
                    ->action(function (Deal $record): void {
                        $record->approve(auth('admin')->id());
                        Notification::make()
                            ->title('Deal approved')
                            ->success()
                            ->send();
                    }),
                TableAction::make('reject')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn (Deal $record): bool => $record->isPending())
                    ->form([
                        \Filament\Forms\Components\Textarea::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->required()
                            ->maxLength(500),
                    ])
                    ->action(function (Deal $record, array $data): void {
                        $record->reject(auth('admin')->id(), $data['rejection_reason']);
                        Notification::make()
                            ->title('Deal rejected')
                            ->success()
                            ->send();
                    }),
                EditAction::make()->hidden(fn (?Deal $record) => $record?->trashed() ?? false),
                DeleteAction::make()->hidden(fn (?Deal $record) => $record?->trashed() ?? false),
                ForceDeleteAction::make()->visible(fn (?Deal $record) => $record?->trashed() ?? false),
                RestoreAction::make()->visible(fn (?Deal $record) => $record?->trashed() ?? false),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ])
            ->recordUrl(fn (?Deal $record): ?string => ($record?->trashed() || ! Pages\EditDeal::canAccess(['record' => $record])) ? null : Pages\EditDeal::getUrl(['record' => $record]));
    }

    public static function getRelations(): array
    {
        return [
            DealSlotsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeals::route('/'),
            'create' => Pages\CreateDeal::route('/create'),
            'edit' => Pages\EditDeal::route('/{record}/edit'),
        ];
    }

    /**
     * Format a value for display in the changes modal
     */
    protected static function formatValue($value, string $key = '', ?Deal $record = null): string
    {
        if (is_null($value)) {
            return '(empty)';
        }

        // Handle service_type_id specifically
        if ($key === 'service_type_id' && is_numeric($value)) {
            $tag = Tag::find($value);
            return $tag ? $tag->title : "Service Type ID: {$value}";
        }

        // Handle valid_days array - convert to readable day names
        if ($key === 'valid_days' && is_array($value)) {
            $dayNames = Carbon::getDays();
            $selectedDays = [];
            foreach ($value as $dayIndex) {
                if (isset($dayNames[$dayIndex])) {
                    $selectedDays[] = $dayNames[$dayIndex];
                }
            }
            return empty($selectedDays) ? '(no days selected)' : implode(', ', $selectedDays);
        }

        if (is_array($value)) {
            return json_encode($value, JSON_PRETTY_PRINT);
        }

        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }

        return (string) $value;
    }
}
