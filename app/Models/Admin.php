<?php

namespace App\Models;

use App\Contracts\CanUploadReel;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;

class Admin extends Authenticatable implements CanUploadReel, FilamentUser
{
    /** @use HasFactory<\Database\Factories\AdminFactory> */
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];

    public function canAccessPanel(Panel $panel): bool
    {
        return true;
    }

    public function getName(): string
    {
        return $this->name;
    }
}
