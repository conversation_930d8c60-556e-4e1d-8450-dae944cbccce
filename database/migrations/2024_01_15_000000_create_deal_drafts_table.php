<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('deal_drafts', function (Blueprint $table) {
            $table->id();
            
            // Reference to the original deal
            $table->foreignId('original_deal_id')->constrained('deals')->onDelete('cascade');
            
            // Copy all fields from deals table
            $table->string('title');
            $table->text('description');
            $table->string('deal_type');
            $table->decimal('max_saving', 10, 2)->default(0);
            $table->json('valid_days')->nullable();
            $table->integer('max_usage_per_day')->default(1);
            $table->integer('reuse_limit_days');
            
            // Service type (single choice)
            $table->foreignId('service_type_id')->nullable()->constrained('tags')->onDelete('set null');
            
            // Partner location
            $table->foreignId('partner_location_id')->constrained('partner_places')->onDelete('cascade');
            
            // Approval workflow fields
            $table->enum('approval_status', ['draft', 'pending', 'approved', 'rejected'])->default('draft');
            $table->foreignId('submitted_by')->nullable()->constrained('employees')->onDelete('set null');
            $table->timestamp('submitted_at')->nullable();
            $table->foreignId('reviewed_by')->nullable()->constrained('admins')->onDelete('set null');
            $table->timestamp('reviewed_at')->nullable();
            $table->text('rejection_reason')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['original_deal_id', 'approval_status']);
            $table->index('approval_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('deal_drafts');
    }
};
