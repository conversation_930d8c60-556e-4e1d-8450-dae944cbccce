<?php

namespace App\Filament\Resources\PartnerPlaceResource\RelationManagers;

use App\Filament\Resources\ReelResource;
use App\Models\Reel;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;

class ReelsRelationManager extends RelationManager
{
    protected static string $relationship = 'reels';

    public function form(Form $form): Form
    {
        return ReelResource::form($form);
    }

    public function table(Table $table): Table
    {
        return ReelResource::table($table)
            ->headerActions([
                Reel::getUploadReelsAction(),
            ])
            ->recordUrl(fn (Reel $reel) => ReelResource::getUrl('edit', ['record' => $reel->id]))
            ->filters([
                TrashedFilter::make(),
            ]);
    }
}
