<?php

namespace Tests\Feature;

use App\Enums\ApprovalStatus;
use App\Filament\Resources\DealResource;
use App\Models\Admin;
use App\Models\Deal;
use App\Models\Employee;
use App\Models\Partner;
use App\Models\PartnerLocation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class AdminDealApprovalTest extends TestCase
{
    use RefreshDatabase;

    protected Employee $employee;

    protected Admin $admin;

    protected PartnerLocation $partnerLocation;

    protected Deal $deal;

    protected function setUp(): void
    {
        parent::setUp();

        $this->employee = Employee::factory()->create();
        $this->admin = Admin::factory()->create();

        $partner = Partner::factory()->create();
        $this->partnerLocation = PartnerLocation::factory()->create([
            'partner_id' => $partner->id,
        ]);

        $this->deal = Deal::factory()->create([
            'partner_location_id' => $this->partnerLocation->id,
            'approval_status' => ApprovalStatus::PENDING,
            'pending_data' => [
                'title' => 'Updated Deal Title',
                'description' => 'Updated Description',
                'max_saving' => 100,
            ],
            'submitted_by' => $this->employee->id,
            'submitted_at' => now(),
        ]);
    }

    /** @test */
    public function admin_can_view_deals_list()
    {
        $this->actingAs($this->admin, 'admin');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertSuccessful()
            ->assertCanSeeTableRecords([$this->deal]);
    }

    /** @test */
    public function admin_can_see_approval_status_column()
    {
        $this->actingAs($this->admin, 'admin');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertCanRenderTableColumn('approval_status')
            ->assertCanRenderTableColumn('submittedBy.name')
            ->assertCanRenderTableColumn('submitted_at')
            ->assertCanRenderTableColumn('reviewedBy.name')
            ->assertCanRenderTableColumn('reviewed_at');
    }

    /** @test */
    public function admin_can_filter_by_approval_status()
    {
        $draftDeal = Deal::factory()->create(['approval_status' => ApprovalStatus::DRAFT]);
        $approvedDeal = Deal::factory()->create(['approval_status' => ApprovalStatus::APPROVED]);

        $this->actingAs($this->admin, 'admin');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->filterTable('approval_status', ApprovalStatus::PENDING->value)
            ->assertCanSeeTableRecords([$this->deal])
            ->assertCanNotSeeTableRecords([$draftDeal, $approvedDeal]);
    }

    /** @test */
    public function admin_sees_view_changes_action_for_pending_deals()
    {
        $this->actingAs($this->admin, 'admin');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertTableActionExists('view_changes', $this->deal);
    }

    /** @test */
    public function admin_sees_approve_action_for_pending_deals()
    {
        $this->actingAs($this->admin, 'admin');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertTableActionExists('approve', $this->deal);
    }

    /** @test */
    public function admin_sees_reject_action_for_pending_deals()
    {
        $this->actingAs($this->admin, 'admin');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertTableActionExists('reject', $this->deal);
    }

    /** @test */
    public function admin_does_not_see_approval_actions_for_non_pending_deals()
    {
        $draftDeal = Deal::factory()->create(['approval_status' => ApprovalStatus::DRAFT]);
        $this->actingAs($this->admin, 'admin');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertTableActionHidden('view_changes', $draftDeal)
            ->assertTableActionHidden('approve', $draftDeal)
            ->assertTableActionHidden('reject', $draftDeal);
    }

    /** @test */
    public function admin_can_approve_pending_deal()
    {
        $this->actingAs($this->admin, 'admin');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->callTableAction('approve', $this->deal);

        $this->deal->refresh();
        $this->assertTrue($this->deal->isApproved());
        $this->assertEquals('Updated Deal Title', $this->deal->title);
        $this->assertEquals('Updated Description', $this->deal->description);
        $this->assertEquals(100, $this->deal->max_saving);
        $this->assertNull($this->deal->pending_data);
        $this->assertEquals($this->admin->id, $this->deal->reviewed_by);
        $this->assertNotNull($this->deal->reviewed_at);
    }

    /** @test */
    public function admin_can_reject_pending_deal()
    {
        $this->actingAs($this->admin, 'admin');
        $rejectionReason = 'The title is not appropriate for our platform.';

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->callTableAction('reject', $this->deal, data: [
                'rejection_reason' => $rejectionReason,
            ]);

        $this->deal->refresh();
        $this->assertTrue($this->deal->isRejected());
        $this->assertEquals($rejectionReason, $this->deal->rejection_reason);
        $this->assertEquals($this->admin->id, $this->deal->reviewed_by);
        $this->assertNotNull($this->deal->reviewed_at);

        // Original data should remain unchanged
        $this->assertNotEquals('Updated Deal Title', $this->deal->title);
    }

    /** @test */
    public function admin_cannot_reject_without_reason()
    {
        $this->actingAs($this->admin, 'admin');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->callTableAction('reject', $this->deal, data: [
                'rejection_reason' => '',
            ])
            ->assertHasTableActionErrors(['rejection_reason' => 'required']);

        $this->deal->refresh();
        $this->assertTrue($this->deal->isPending()); // Should still be pending
    }

    /** @test */
    public function admin_can_view_changes_modal()
    {
        $this->actingAs($this->admin, 'admin');

        $component = Livewire::test(DealResource\Pages\ListDeals::class)
            ->callTableAction('view_changes', $this->deal);

        // The modal should show the changes
        $component->assertSee('Pending Changes')
            ->assertSee('Updated Deal Title')
            ->assertSee('Updated Description')
            ->assertSee($this->employee->name);
    }

    /** @test */
    public function admin_can_see_submission_details()
    {
        $this->actingAs($this->admin, 'admin');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertTableColumnStateSet('submittedBy.name', $this->employee->name, $this->deal)
            ->assertTableColumnStateSet('submitted_at', $this->deal->submitted_at->format('M j, Y'), $this->deal);
    }

    /** @test */
    public function admin_can_see_review_details_after_approval()
    {
        $this->deal->approve($this->admin->id);
        $this->actingAs($this->admin, 'admin');

        Livewire::test(DealResource\Pages\ListDeals::class)
            ->assertTableColumnStateSet('reviewedBy.name', $this->admin->name, $this->deal)
            ->assertTableColumnStateSet('reviewed_at', $this->deal->reviewed_at->format('M j, Y'), $this->deal);
    }

    /** @test */
    public function admin_can_see_rejection_reason_in_deal_details()
    {
        $rejectionReason = 'Title needs improvement';
        $this->deal->reject($this->admin->id, $rejectionReason);

        $this->actingAs($this->admin, 'admin');

        // When viewing the deal details, rejection reason should be visible
        Livewire::test(DealResource\Pages\EditDeal::class, ['record' => $this->deal->id])
            ->assertSee($rejectionReason);
    }

    /** @test */
    public function pending_deals_count_is_tracked()
    {
        // Create multiple pending deals
        Deal::factory()->count(3)->create([
            'approval_status' => ApprovalStatus::PENDING,
            'submitted_by' => $this->employee->id,
            'submitted_at' => now(),
        ]);

        $this->actingAs($this->admin, 'admin');

        $pendingCount = Deal::pending()->count();
        $this->assertEquals(4, $pendingCount); // 3 new + 1 from setUp
    }
}
