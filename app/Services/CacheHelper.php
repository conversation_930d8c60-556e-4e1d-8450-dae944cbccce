<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Contracts\Cache\Repository as CacheRepository;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Nuwave\Lighthouse\Cache\CacheKeyAndTags;

/**
 * Cache Helper - Programmatic cache clearing with same functionality as @clearCache directive
 *
 * Usage Examples:
 *
 * 1. Clear specific field cache:
 *    app(CacheHelper::class)->clearFieldCache('User', 123, 'profile');
 *
 * 2. Clear entire type cache:
 *    app(CacheHelper::class)->clearTypeCache('User', 123);
 *
 * 3. Clear by argument (equivalent to @clearCache(type: "User", idSource: {argument: "input.id"})):
 *    app(CacheHelper::class)->clearCacheByArgument('User', 'input.id', $args);
 *
 * 4. Clear by result field (equivalent to @clearCache(type: "User", idSource: {field: "user.id"})):
 *    app(CacheHelper::class)->clearCacheByResultField('User', 'user.id', $result);
 *
 * 5. Manual cache clearing (exact same as directive):
 *    app(CacheHelper::class)->clearCache(
 *        type: 'User',
 *        idSource: ['argument' => 'input.id'],
 *        field: 'profile',
 *        args: $args
 *    );
 */
class CacheHelper
{
    public function __construct(
        protected CacheRepository $cacheRepository,
        protected CacheKeyAndTags $cacheKeyAndTags,
    ) {}

    /**
     * Clear cache by tags with the same logic as ClearCacheDirective
     *
     * @param  string  $type  Name of the parent type of the field to clear
     * @param  array|null  $idSource  Source of the parent ID to clear (argument or field path)
     * @param  string|null  $field  Name of the field to clear
     * @param  mixed  $result  The result data (when using idSource.field)
     * @param  array  $args  The arguments data (when using idSource.argument)
     */
    public function clearCache(
        string $type,
        ?array $idSource = null,
        ?string $field = null,
        mixed $result = null,
        array $args = []
    ): void {
        if (isset($idSource['argument'])) {
            $idOrIds = Arr::get($args, $idSource['argument']);
        } elseif (isset($idSource['field'])) {
            $idOrIds = data_get($result, $idSource['field']);
        } else {
            $idOrIds = [null];
        }

        foreach ((array) $idOrIds as $id) {
            $tag = is_string($field)
                ? $this->cacheKeyAndTags->fieldTag($type, $id, $field)
                : $this->cacheKeyAndTags->parentTag($type, $id);

            // Use Cache facade which properly handles tagging support
            try {
                Cache::tags([$tag])->flush();
            } catch (\BadMethodCallException $e) {
                // Fallback for cache stores that don't support tags
                Cache::forget($tag);
            }
        }
    }

    /**
     * Clear cache for a specific type and field
     *
     * @param  string  $type  The GraphQL type name
     * @param  mixed  $id  The ID of the entity
     * @param  string  $field  The field name to clear
     */
    public function clearFieldCache(string $type, mixed $id, string $field): void
    {
        $tag = $this->cacheKeyAndTags->fieldTag($type, $id, $field);

        try {
            Cache::tags([$tag])->flush();
        } catch (\BadMethodCallException $e) {
            Cache::forget($tag);
        }
    }

    /**
     * Clear cache for an entire type
     *
     * @param  string  $type  The GraphQL type name
     * @param  mixed  $id  The ID of the entity (optional)
     */
    public function clearTypeCache(string $type, mixed $id = null): void
    {
        $tag = $this->cacheKeyAndTags->parentTag($type, $id);

        try {
            Cache::tags([$tag])->flush();
        } catch (\BadMethodCallException $e) {
            Cache::forget($tag);
        }
    }

    /**
     * Clear cache using argument path (like @clearCache with idSource.argument)
     *
     * @param  string  $type  The GraphQL type name
     * @param  string  $argumentPath  Dot notation path to the argument (e.g., "input.id")
     * @param  array  $args  The mutation/query arguments
     * @param  string|null  $field  Optional field name
     */
    public function clearCacheByArgument(
        string $type,
        string $argumentPath,
        array $args,
        ?string $field = null
    ): void {
        $this->clearCache(
            type: $type,
            idSource: ['argument' => $argumentPath],
            field: $field,
            args: $args
        );
    }

    /**
     * Clear cache using result field path (like @clearCache with idSource.field)
     *
     * @param  string  $type  The GraphQL type name
     * @param  string  $fieldPath  Dot notation path to the field in result (e.g., "user.id")
     * @param  mixed  $result  The mutation/query result
     * @param  string|null  $field  Optional field name
     */
    public function clearCacheByResultField(
        string $type,
        string $fieldPath,
        mixed $result,
        ?string $field = null
    ): void {
        $this->clearCache(
            type: $type,
            idSource: ['field' => $fieldPath],
            field: $field,
            result: $result
        );
    }
}
