<?php

namespace App\Filament\Employee\Resources\DealResource\Pages;

use App\Enums\ApprovalStatus;
use App\Filament\Employee\Resources\DealResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateDeal extends CreateRecord
{
    protected static string $resource = DealResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set initial approval status to draft for new deals created by staff
        $data['approval_status'] = ApprovalStatus::DRAFT;

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('edit', ['record' => $this->getRecord()]);
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Deal created')
            ->body('The deal has been created as a draft. Submit it for approval when ready.');
    }
}
