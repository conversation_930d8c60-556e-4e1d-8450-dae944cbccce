<?php

return [
    /*
    |--------------------------------------------------------------------------
    | FCM Server Key
    |--------------------------------------------------------------------------
    |
    | This is the server key from your Firebase Console.
    |
    */
    'server_key' => env('FCM_SERVER_KEY'),

    /*
    |--------------------------------------------------------------------------
    | FCM Sender ID
    |--------------------------------------------------------------------------
    |
    | This is the sender ID from your Firebase Console.
    |
    */
    'sender_id' => env('FCM_SENDER_ID'),

    /*
    |--------------------------------------------------------------------------
    | FCM Server Group Key
    |--------------------------------------------------------------------------
    |
    | This is the server group key from your Firebase Console.
    |
    */
    'server_group_key' => env('FCM_SERVER_GROUP_KEY'),

    /*
    |--------------------------------------------------------------------------
    | FCM Default Channel
    |--------------------------------------------------------------------------
    |
    | This is the default channel that will be used for notifications.
    |
    */
    'default_channel' => env('FCM_DEFAULT_CHANNEL', 'default'),

    /*
    |--------------------------------------------------------------------------
    | FCM Default Sound
    |--------------------------------------------------------------------------
    |
    | This is the default sound that will be used for notifications.
    |
    */
    'default_sound' => env('FCM_DEFAULT_SOUND', 'default'),

    /*
    |--------------------------------------------------------------------------
    | FCM Default Icon
    |--------------------------------------------------------------------------
    |
    | This is the default icon that will be used for notifications.
    |
    */
    'default_icon' => env('FCM_DEFAULT_ICON', 'default'),

    /*
    |--------------------------------------------------------------------------
    | FCM Default Color
    |--------------------------------------------------------------------------
    |
    | This is the default color that will be used for notifications.
    |
    */
    'default_color' => env('FCM_DEFAULT_COLOR', '#000000'),
];
